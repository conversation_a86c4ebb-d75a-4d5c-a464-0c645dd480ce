#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
独立的股票数据查看器
绕过VeighNa界面问题，直接查看股票数据
"""

import sys
import sqlite3
import pandas as pd
from datetime import datetime, timedelta
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                               QHBoxLayout, QLineEdit, QComboBox, QPushButton, 
                               QTableWidget, QTableWidgetItem, QLabel, QDateEdit,
                               QMessageBox, QHeaderView)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QFont

class StockDataViewer(QMainWindow):
    """股票数据查看器"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.load_available_stocks()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("VeighNa 股票数据查看器")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央窗口
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        layout = QVBoxLayout(central_widget)
        
        # 查询区域
        query_layout = QHBoxLayout()
        
        # 股票代码输入
        query_layout.addWidget(QLabel("股票代码:"))
        self.symbol_input = QLineEdit()
        self.symbol_input.setPlaceholderText("输入股票代码，如: 000001")
        query_layout.addWidget(self.symbol_input)
        
        # 交易所选择
        query_layout.addWidget(QLabel("交易所:"))
        self.exchange_combo = QComboBox()
        self.exchange_combo.addItems(["SZSE", "SSE"])
        query_layout.addWidget(self.exchange_combo)
        
        # 开始日期
        query_layout.addWidget(QLabel("开始日期:"))
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setCalendarPopup(True)
        query_layout.addWidget(self.start_date)
        
        # 结束日期
        query_layout.addWidget(QLabel("结束日期:"))
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setCalendarPopup(True)
        query_layout.addWidget(self.end_date)
        
        # 查询按钮
        self.query_button = QPushButton("查询数据")
        self.query_button.clicked.connect(self.query_data)
        query_layout.addWidget(self.query_button)
        
        layout.addLayout(query_layout)
        
        # 状态标签
        self.status_label = QLabel("准备就绪")
        layout.addWidget(self.status_label)
        
        # 数据表格
        self.data_table = QTableWidget()
        self.data_table.setColumnCount(7)
        self.data_table.setHorizontalHeaderLabels([
            "日期", "开盘价", "最高价", "最低价", "收盘价", "成交量", "成交额"
        ])
        
        # 设置表格样式
        header = self.data_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        
        layout.addWidget(self.data_table)
        
        # 设置字体
        font = QFont()
        font.setPointSize(10)
        self.setFont(font)
    
    def load_available_stocks(self):
        """加载可用股票列表"""
        try:
            conn = sqlite3.connect('database.db')
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM dbbardata")
            count = cursor.fetchone()[0]
            
            if count > 0:
                cursor.execute("SELECT DISTINCT symbol, exchange FROM dbbardata ORDER BY symbol LIMIT 10")
                stocks = cursor.fetchall()
                
                stock_list = [f"{symbol}.{exchange}" for symbol, exchange in stocks]
                self.status_label.setText(f"数据库中有 {count:,} 条数据，可查询股票: {', '.join(stock_list[:5])}...")
            else:
                self.status_label.setText("数据库为空，请先下载数据")
            
            conn.close()
            
        except Exception as e:
            self.status_label.setText(f"数据库连接失败: {e}")
    
    def query_data(self):
        """查询股票数据"""
        symbol = self.symbol_input.text().strip()
        exchange = self.exchange_combo.currentText()
        start_date = self.start_date.date().toString("yyyy-MM-dd")
        end_date = self.end_date.date().toString("yyyy-MM-dd")
        
        if not symbol:
            QMessageBox.warning(self, "警告", "请输入股票代码")
            return
        
        try:
            conn = sqlite3.connect('database.db')
            
            # 查询数据
            query = """
                SELECT datetime, open, high, low, close, volume, turnover
                FROM dbbardata 
                WHERE symbol = ? AND exchange = ? 
                AND date(datetime) BETWEEN ? AND ?
                ORDER BY datetime DESC
            """
            
            data = pd.read_sql(query, conn, params=[symbol, exchange, start_date, end_date])
            
            if data.empty:
                self.status_label.setText(f"没有找到 {symbol}.{exchange} 的数据")
                self.data_table.setRowCount(0)
            else:
                self.status_label.setText(f"找到 {len(data)} 条 {symbol}.{exchange} 的数据")
                
                # 填充表格
                self.data_table.setRowCount(len(data))
                
                for row, (_, record) in enumerate(data.iterrows()):
                    self.data_table.setItem(row, 0, QTableWidgetItem(record['datetime'][:10]))
                    self.data_table.setItem(row, 1, QTableWidgetItem(f"{record['open']:.2f}"))
                    self.data_table.setItem(row, 2, QTableWidgetItem(f"{record['high']:.2f}"))
                    self.data_table.setItem(row, 3, QTableWidgetItem(f"{record['low']:.2f}"))
                    self.data_table.setItem(row, 4, QTableWidgetItem(f"{record['close']:.2f}"))
                    self.data_table.setItem(row, 5, QTableWidgetItem(f"{record['volume']:,.0f}"))
                    self.data_table.setItem(row, 6, QTableWidgetItem(f"{record['turnover']:,.0f}"))
            
            conn.close()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"查询失败: {e}")
            self.status_label.setText(f"查询失败: {e}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    viewer = StockDataViewer()
    viewer.show()
    
    print("🚀 独立股票数据查看器已启动")
    print("=" * 40)
    print("📊 使用说明:")
    print("1. 输入股票代码（如: 000001）")
    print("2. 选择交易所（SZSE或SSE）")
    print("3. 设置查询日期范围")
    print("4. 点击'查询数据'按钮")
    print("=" * 40)
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
