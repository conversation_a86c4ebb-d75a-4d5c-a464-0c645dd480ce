#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
直接查看VeighNa数据库中的股票数据
"""

import sqlite3
import pandas as pd
from datetime import datetime

def show_available_stocks():
    """显示可用的股票列表"""
    print("📋 可用的股票列表")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('database.db')
        
        # 获取所有股票代码
        stocks = pd.read_sql("""
            SELECT DISTINCT symbol, exchange, COUNT(*) as data_count,
                   MIN(datetime) as start_date, MAX(datetime) as end_date
            FROM dbbardata 
            GROUP BY symbol, exchange 
            ORDER BY symbol
        """, conn)
        
        print(f"总共有 {len(stocks)} 只股票的数据：\n")
        
        for _, stock in stocks.iterrows():
            print(f"📈 {stock['symbol']}.{stock['exchange']}")
            print(f"   数据量: {stock['data_count']:,} 条")
            print(f"   时间范围: {stock['start_date'][:10]} 到 {stock['end_date'][:10]}")
            print()
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")

def show_stock_data(symbol, exchange="SZSE", limit=10):
    """显示指定股票的数据"""
    print(f"📊 {symbol}.{exchange} 的最新数据")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('database.db')
        
        # 获取股票数据
        data = pd.read_sql(f"""
            SELECT datetime, open, high, low, close, volume, turnover
            FROM dbbardata 
            WHERE symbol = '{symbol}' AND exchange = '{exchange}'
            ORDER BY datetime DESC
            LIMIT {limit}
        """, conn)
        
        if data.empty:
            print(f"❌ 没有找到 {symbol}.{exchange} 的数据")
            print("💡 请检查股票代码和交易所是否正确")
        else:
            print(f"✅ 找到 {len(data)} 条最新数据：\n")
            
            # 格式化显示
            for _, row in data.iterrows():
                date = row['datetime'][:10]
                print(f"📅 {date}")
                print(f"   开盘: {row['open']:.2f}")
                print(f"   最高: {row['high']:.2f}")
                print(f"   最低: {row['low']:.2f}")
                print(f"   收盘: {row['close']:.2f}")
                print(f"   成交量: {row['volume']:,.0f}")
                print(f"   成交额: {row['turnover']:,.0f}")
                print()
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")

def main():
    """主函数"""
    print("🔍 VeighNa股票数据查看工具")
    print("=" * 60)
    
    # 显示可用股票
    show_available_stocks()
    
    print("\n" + "=" * 60)
    print("📊 热门股票数据示例")
    print("=" * 60)
    
    # 显示几只热门股票的数据
    popular_stocks = [
        ("000001", "SZSE", "平安银行"),
        ("000002", "SZSE", "万科A"),
        ("600000", "SSE", "浦发银行"),
        ("600036", "SSE", "招商银行")
    ]
    
    for symbol, exchange, name in popular_stocks:
        print(f"\n🏢 {name} ({symbol}.{exchange})")
        print("-" * 40)
        show_stock_data(symbol, exchange, 3)

if __name__ == "__main__":
    main()
