#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试AkShare数据源配置

本脚本用于验证AkShare数据源是否正确配置和可用
"""

import json
import os
from vnpy.trader.datafeed import get_datafeed
from vnpy.trader.setting import SETTINGS

def test_akshare_config():
    """
    测试AkShare配置
    """
    print("=== 测试AkShare数据源配置 ===")
    
    # 1. 检查配置文件
    config_file = "vt_setting.json"
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print("✓ 配置文件存在")
            print(f"  数据源名称: {config.get('datafeed.name', '未配置')}")
            print(f"  用户名: {config.get('datafeed.username', '未配置')}")
            print(f"  密码: {'已配置' if config.get('datafeed.password') else '未配置'}")
            
        except Exception as e:
            print(f"✗ 读取配置文件失败: {e}")
            return False
    else:
        print("✗ 配置文件不存在")
        return False
    
    # 2. 测试数据源加载
    try:
        print("\n=== 测试数据源加载 ===")
        
        # 获取数据源
        datafeed = get_datafeed()
        print(f"✓ 数据源类型: {datafeed.__class__.__name__}")
        print(f"✓ 数据源模块: {datafeed.__class__.__module__}")
        
        # 检查是否为AkShare数据源
        if 'akshare' in datafeed.__class__.__module__.lower():
            print("✓ AkShare数据源加载成功")
            return True
        else:
            print(f"⚠ 当前数据源不是AkShare: {datafeed.__class__.__module__}")
            return False
            
    except Exception as e:
        print(f"✗ 数据源加载失败: {e}")
        return False

def test_akshare_import():
    """
    测试AkShare模块导入
    """
    print("\n=== 测试AkShare模块导入 ===")
    
    try:
        import vnpy_akshare
        print(f"✓ vnpy_akshare模块导入成功")
        print(f"  模块路径: {vnpy_akshare.__file__}")
        
        # 测试Datafeed类
        from vnpy_akshare import Datafeed
        print(f"✓ AkShare Datafeed类导入成功")
        
        # 创建实例测试
        datafeed_instance = Datafeed()
        print(f"✓ AkShare Datafeed实例创建成功")
        
        return True
        
    except ImportError as e:
        print(f"✗ vnpy_akshare模块导入失败: {e}")
        print("  请运行: pip install vnpy_akshare")
        return False
    except Exception as e:
        print(f"✗ AkShare测试失败: {e}")
        return False

def test_akshare_data():
    """
    测试AkShare数据获取
    """
    print("\n=== 测试AkShare数据获取 ===")
    
    try:
        import akshare as ak
        print("✓ akshare库导入成功")
        
        # 测试获取股票基本信息
        stock_info = ak.stock_info_a_code_name()
        if not stock_info.empty:
            print(f"✓ 获取A股代码表成功，共{len(stock_info)}只股票")
            
            # 显示前5只股票信息
            print("\n前5只股票信息:")
            for i, (_, row) in enumerate(stock_info.head().iterrows()):
                print(f"  {row['code']} - {row['name']}")
            
            return True
        else:
            print("✗ 获取A股代码表为空")
            return False
            
    except ImportError:
        print("✗ akshare库未安装")
        print("  请运行: pip install akshare")
        return False
    except Exception as e:
        print(f"✗ 数据获取测试失败: {e}")
        return False

def show_usage_guide():
    """
    显示使用指南
    """
    print("\n=== 使用指南 ===")
    print("\n1. 启动VeighNa平台:")
    print("   python start_vnpy_a_stock.py")
    
    print("\n2. 下载历史数据:")
    print("   - 打开'功能' → '数据管理'")
    print("   - 选择交易所: SSE 或 SZSE")
    print("   - 输入股票代码: 如 600000")
    print("   - 设置时间范围")
    print("   - 点击'下载数据'")
    
    print("\n3. 订阅实时行情:")
    print("   - 在主界面交易组件中")
    print("   - 选择交易所: SSE 或 SZSE")
    print("   - 输入股票代码: 如 600000")
    print("   - 按回车键订阅")
    
    print("\n4. 常用股票代码:")
    print("   上证: 600000(浦发银行), 600036(招商银行), 600519(贵州茅台)")
    print("   深证: 000001(平安银行), 000002(万科A), 000858(五粮液)")

def main():
    """
    主函数
    """
    print("VeighNa AkShare数据源配置测试工具")
    print("=" * 50)
    
    # 运行所有测试
    tests = [
        ("配置文件测试", test_akshare_config),
        ("模块导入测试", test_akshare_import),
        ("数据获取测试", test_akshare_data),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果汇总
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    all_passed = True
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！AkShare数据源配置正确。")
        show_usage_guide()
    else:
        print("\n⚠ 部分测试失败，请检查配置和安装。")
        print("\n解决方案:")
        print("1. 确保已安装: pip install vnpy_akshare")
        print("2. 确保已安装: pip install akshare")
        print("3. 检查配置文件 vt_setting.json")
        print("4. 重新运行测试")

if __name__ == "__main__":
    main()