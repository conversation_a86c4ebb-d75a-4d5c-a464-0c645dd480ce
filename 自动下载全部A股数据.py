#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
自动下载全部A股数据到本地数据库
一键批量下载所有A股历史数据，解决手动下载的繁琐问题
"""

import akshare as ak
import pandas as pd
from datetime import datetime, timedelta
import time
import json
from pathlib import Path
import sqlite3
import os
from tqdm import tqdm

class AStockDataDownloader:
    """A股数据自动下载器"""
    
    def __init__(self, db_path="database.db", start_date=None, end_date=None):
        self.db_path = db_path
        self.start_date = start_date or (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')  # 默认下载1年数据
        self.end_date = end_date or datetime.now().strftime('%Y%m%d')
        self.failed_stocks = []
        self.success_count = 0
        self.total_bars = 0
        
    def get_all_a_stocks(self):
        """获取所有A股股票列表"""
        print("正在获取A股股票列表...")
        try:
            # 获取A股股票信息
            stock_info = ak.stock_info_a_code_name()
            print(f"✅ 获取到 {len(stock_info)} 只A股股票")
            return stock_info
        except Exception as e:
            print(f"❌ 获取股票列表失败: {e}")
            return None
    
    def create_database_table(self):
        """创建数据库表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建股票数据表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS stock_daily_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    exchange TEXT NOT NULL,
                    date TEXT NOT NULL,
                    open_price REAL,
                    high_price REAL,
                    low_price REAL,
                    close_price REAL,
                    volume INTEGER,
                    amount REAL,
                    turnover_rate REAL,
                    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(symbol, exchange, date)
                )
            ''')
            
            # 创建下载记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS download_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    exchange TEXT NOT NULL,
                    start_date TEXT,
                    end_date TEXT,
                    bars_count INTEGER,
                    download_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    status TEXT DEFAULT 'success'
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ 数据库表创建成功")
            return True
        except Exception as e:
            print(f"❌ 数据库表创建失败: {e}")
            return False
    
    def download_stock_data(self, symbol, name, exchange):
        """下载单只股票的历史数据"""
        try:
            # 获取股票历史数据
            df = ak.stock_zh_a_hist(
                symbol=symbol, 
                period="daily", 
                start_date=self.start_date, 
                end_date=self.end_date
            )
            
            if df is None or len(df) == 0:
                return 0, f"无数据"
            
            # 数据预处理
            df['symbol'] = symbol
            df['exchange'] = exchange
            df['date'] = df['日期'].astype(str)
            df['open_price'] = df['开盘'].astype(float)
            df['high_price'] = df['最高'].astype(float)
            df['low_price'] = df['最低'].astype(float)
            df['close_price'] = df['收盘'].astype(float)
            df['volume'] = df['成交量'].astype(int)
            df['amount'] = df['成交额'].astype(float)
            df['turnover_rate'] = df.get('换手率', 0).astype(float)
            
            # 选择需要的列
            columns = ['symbol', 'exchange', 'date', 'open_price', 'high_price', 
                      'low_price', 'close_price', 'volume', 'amount', 'turnover_rate']
            df_clean = df[columns]
            
            # 保存到数据库
            conn = sqlite3.connect(self.db_path)
            df_clean.to_sql('stock_daily_data', conn, if_exists='append', index=False)
            
            # 记录下载信息
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO download_records 
                (symbol, exchange, start_date, end_date, bars_count, status)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (symbol, exchange, self.start_date, self.end_date, len(df_clean), 'success'))
            
            conn.commit()
            conn.close()
            
            return len(df_clean), "成功"
            
        except Exception as e:
            # 记录失败信息
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO download_records 
                    (symbol, exchange, start_date, end_date, bars_count, status)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (symbol, exchange, self.start_date, self.end_date, 0, f'failed: {str(e)}'))
                conn.commit()
                conn.close()
            except:
                pass
            
            return 0, str(e)
    
    def download_all_stocks(self, max_stocks=None, delay=0.1):
        """批量下载所有股票数据"""
        print(f"开始批量下载A股数据 (时间范围: {self.start_date} - {self.end_date})")
        print("=" * 60)
        
        # 获取股票列表
        stock_list = self.get_all_a_stocks()
        if stock_list is None:
            return False
        
        # 创建数据库表
        if not self.create_database_table():
            return False
        
        # 限制下载数量（用于测试）
        if max_stocks:
            stock_list = stock_list.head(max_stocks)
            print(f"⚠️  测试模式：仅下载前 {max_stocks} 只股票")
        
        total_stocks = len(stock_list)
        print(f"准备下载 {total_stocks} 只股票的历史数据...\n")
        
        # 使用进度条
        with tqdm(total=total_stocks, desc="下载进度", unit="股") as pbar:
            for index, row in stock_list.iterrows():
                symbol = row['code']
                name = row['name']
                
                # 判断交易所
                if symbol.startswith('6'):
                    exchange = 'SSE'  # 上海证券交易所
                elif symbol.startswith(('0', '3')):
                    exchange = 'SZSE'  # 深圳证券交易所
                else:
                    exchange = 'OTHER'
                
                # 下载数据
                bars_count, status = self.download_stock_data(symbol, name, exchange)
                
                if "成功" in status:
                    self.success_count += 1
                    self.total_bars += bars_count
                    pbar.set_postfix({
                        '成功': self.success_count,
                        '总K线': self.total_bars,
                        '当前': f"{symbol}({name})"
                    })
                else:
                    self.failed_stocks.append({
                        'symbol': symbol,
                        'name': name,
                        'exchange': exchange,
                        'error': status
                    })
                    pbar.set_postfix({
                        '成功': self.success_count,
                        '失败': len(self.failed_stocks),
                        '当前': f"{symbol}({name}) - 失败"
                    })
                
                pbar.update(1)
                
                # 延迟避免请求过快
                if delay > 0:
                    time.sleep(delay)
        
        return True
    
    def generate_report(self):
        """生成下载报告"""
        print("\n" + "=" * 60)
        print("📊 下载完成报告")
        print("=" * 60)
        
        print(f"✅ 成功下载: {self.success_count} 只股票")
        print(f"📈 总K线数据: {self.total_bars:,} 条")
        print(f"❌ 失败股票: {len(self.failed_stocks)} 只")
        print(f"📅 数据时间范围: {self.start_date} - {self.end_date}")
        print(f"💾 数据库文件: {Path(self.db_path).absolute()}")
        
        if self.failed_stocks:
            print("\n❌ 失败股票列表:")
            for stock in self.failed_stocks[:10]:  # 只显示前10个失败的
                print(f"   {stock['symbol']} ({stock['name']}) - {stock['error'][:50]}")
            if len(self.failed_stocks) > 10:
                print(f"   ... 还有 {len(self.failed_stocks) - 10} 只股票失败")
        
        # 数据库统计
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 统计数据库中的数据
            cursor.execute("SELECT COUNT(*) FROM stock_daily_data")
            total_records = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(DISTINCT symbol) FROM stock_daily_data")
            unique_stocks = cursor.fetchone()[0]
            
            cursor.execute("SELECT MIN(date), MAX(date) FROM stock_daily_data")
            date_range = cursor.fetchone()
            
            conn.close()
            
            print(f"\n💾 数据库统计:")
            print(f"   总记录数: {total_records:,} 条")
            print(f"   股票数量: {unique_stocks} 只")
            print(f"   数据范围: {date_range[0]} - {date_range[1]}")
            
        except Exception as e:
            print(f"\n❌ 数据库统计失败: {e}")
    
    def save_failed_list(self, filename="failed_stocks.json"):
        """保存失败股票列表"""
        if self.failed_stocks:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.failed_stocks, f, ensure_ascii=False, indent=2)
            print(f"\n💾 失败股票列表已保存到: {filename}")

def create_vnpy_data_manager_script():
    """创建VeighNa数据管理脚本"""
    script_content = '''
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VeighNa数据管理工具
用于查看和管理下载的A股数据
"""

import sqlite3
import pandas as pd
from datetime import datetime

def show_database_stats(db_path="database.db"):
    """显示数据库统计信息"""
    try:
        conn = sqlite3.connect(db_path)
        
        # 基本统计
        print("=== 数据库统计信息 ===")
        
        # 总记录数
        df = pd.read_sql("SELECT COUNT(*) as total FROM stock_daily_data", conn)
        print(f"总K线记录: {df['total'].iloc[0]:,} 条")
        
        # 股票数量
        df = pd.read_sql("SELECT COUNT(DISTINCT symbol) as count FROM stock_daily_data", conn)
        print(f"股票数量: {df['count'].iloc[0]} 只")
        
        # 日期范围
        df = pd.read_sql("SELECT MIN(date) as min_date, MAX(date) as max_date FROM stock_daily_data", conn)
        print(f"数据范围: {df['min_date'].iloc[0]} - {df['max_date'].iloc[0]}")
        
        # 交易所分布
        print("\n=== 交易所分布 ===")
        df = pd.read_sql("SELECT exchange, COUNT(DISTINCT symbol) as count FROM stock_daily_data GROUP BY exchange", conn)
        for _, row in df.iterrows():
            print(f"{row['exchange']}: {row['count']} 只股票")
        
        # 最新数据的股票
        print("\n=== 最新数据示例 ===")
        df = pd.read_sql("""
            SELECT symbol, exchange, date, close_price 
            FROM stock_daily_data 
            WHERE date = (SELECT MAX(date) FROM stock_daily_data)
            LIMIT 10
        """, conn)
        print(df.to_string(index=False))
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")

def search_stock_data(symbol, db_path="database.db"):
    """查询特定股票的数据"""
    try:
        conn = sqlite3.connect(db_path)
        
        df = pd.read_sql("""
            SELECT date, open_price, high_price, low_price, close_price, volume
            FROM stock_daily_data 
            WHERE symbol = ?
            ORDER BY date DESC
            LIMIT 20
        """, conn, params=[symbol])
        
        if len(df) > 0:
            print(f"\n=== {symbol} 最近20天数据 ===")
            print(df.to_string(index=False))
        else:
            print(f"❌ 未找到股票 {symbol} 的数据")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")

if __name__ == "__main__":
    print("VeighNa数据管理工具")
    print("=" * 40)
    
    # 显示统计信息
    show_database_stats()
    
    # 示例：查询特定股票
    print("\n" + "=" * 40)
    search_stock_data("000001")  # 平安银行
'''
    
    with open("vnpy_data_manager.py", 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print("✅ VeighNa数据管理脚本已创建: vnpy_data_manager.py")

def main():
    """主函数"""
    print("🚀 A股数据自动下载工具")
    print("=" * 60)
    
    # 配置选项
    print("请选择下载模式:")
    print("1. 测试模式 (下载前50只股票)")
    print("2. 快速模式 (下载最近3个月数据)")
    print("3. 标准模式 (下载最近1年数据)")
    print("4. 完整模式 (下载最近3年数据)")
    print("5. 自定义模式")
    
    try:
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == "1":
            # 测试模式
            downloader = AStockDataDownloader()
            downloader.download_all_stocks(max_stocks=50, delay=0.2)
            
        elif choice == "2":
            # 快速模式 - 3个月
            start_date = (datetime.now() - timedelta(days=90)).strftime('%Y%m%d')
            downloader = AStockDataDownloader(start_date=start_date)
            downloader.download_all_stocks(delay=0.1)
            
        elif choice == "3":
            # 标准模式 - 1年
            downloader = AStockDataDownloader()
            downloader.download_all_stocks(delay=0.1)
            
        elif choice == "4":
            # 完整模式 - 3年
            start_date = (datetime.now() - timedelta(days=1095)).strftime('%Y%m%d')
            downloader = AStockDataDownloader(start_date=start_date)
            downloader.download_all_stocks(delay=0.05)
            
        elif choice == "5":
            # 自定义模式
            start_date = input("请输入开始日期 (格式: YYYYMMDD): ").strip()
            end_date = input("请输入结束日期 (格式: YYYYMMDD, 回车使用今天): ").strip()
            if not end_date:
                end_date = datetime.now().strftime('%Y%m%d')
            
            max_stocks = input("限制股票数量 (回车下载全部): ").strip()
            max_stocks = int(max_stocks) if max_stocks else None
            
            downloader = AStockDataDownloader(start_date=start_date, end_date=end_date)
            downloader.download_all_stocks(max_stocks=max_stocks, delay=0.1)
            
        else:
            print("❌ 无效选择")
            return
        
        # 生成报告
        downloader.generate_report()
        downloader.save_failed_list()
        
        # 创建数据管理工具
        create_vnpy_data_manager_script()
        
        print("\n🎉 数据下载完成！")
        print("\n📋 后续操作:")
        print("1. 运行 'python vnpy_data_manager.py' 查看数据统计")
        print("2. 启动VeighNa查看下载的数据")
        print("3. 如有失败的股票，可查看 'failed_stocks.json' 文件")
        
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断下载")
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")

if __name__ == "__main__":
    main()