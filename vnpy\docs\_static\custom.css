@import url("vendor.css");

html {
  position: relative;
  min-height: 100%;
}

body {
  /* Margin bottom by footer height */
  margin-bottom: 60px;
  /*padding-top: 50px; */ /* uncomment if navbar should be fixed */
}

body,
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Lato","Helvetica Neue",Helvetica,Arial,sans-serif;
}

h1 {
  font-size: 42px;
  font-weight: 500;
}

h2 {
  font-weight: 300;
  font-size: 34px;
}

ul, ol {
  margin: 0 0 10px 20px;
  padding: 0;
}

code {
  padding: 0;
}

/* -- sidebar --------------------------------------------------------------- */
.page-sidebar {
  margin-top: 25px;
  border: 1px solid #cad7e1;
  background-color: #FFF;
}

.search > input[type="text"] {
  width: 165px;
}

/* dirty but works ¯\_(ツ)_/¯ */
@media (max-width:1200px) and (min-width:768px) {
  .search > input[type="text"] {
    width: 100px;
  }
}

.page-sidebarwrapper {
  padding: 10px;
  line-height: 1.3;
}

.page-sidebarwrapper ul li a {
  display: block;
  padding: 3px 0;
}

a.reference {
  border: none;
  text-decoration: none;
}

a.reference:hover {
  text-decoration: underline;
  border: none;
}

a.reference:active {
  text-decoration: underline;
  border: none;
}


a:visited {
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

a:active {
    text-decoration: underline;
}

img.screenshot {
  width: 100%;
  box-shadow: 0 4px 16px rgba(85, 85, 85, 0.8);
  border-radius: 6px;
  margin: 15px 0;
}

.page-content {
  padding-top: 0px;
  margin-top: 25px;
  margin-bottom: 20px;
}

.paper {
  background-color: #FFF;
  padding: 20px;
  border: 1px solid #cad7e1;
  position: relative;
}

.paper:before {
  content: "";
  position: absolute;
  top: -1px;
  right: -1px;
  border-width: 0 40px 40px 0;
  border-style: solid;
  border-color: #ccc #F6F9FC;
  z-index: 3;
}

footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  /* Set the fixed height of the footer here */
  height: 60px;
  padding: 20px 0;
  background-color: #333333;
}

.copyright,
.made-in,
.powered-by {
  margin: 0;
}

pre {
  display: block;
  margin: 0 0 12px;
  line-height: 1.42857;
  word-break: break-all;
  word-wrap: break-word;
  color: #333;
  background-color: #f5f5f5;
  border: 1px solid #cad7e1;
  border-radius: 0;
}
