{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "\n", "from vnpy.trader.optimize import OptimizationSetting\n", "from vnpy_ctastrategy.backtesting import BacktestingEngine\n", "from vnpy_ctastrategy.strategies.atr_rsi_strategy import AtrRsiStrategy"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["engine = BacktestingEngine()\n", "engine.set_parameters(\n", "    vt_symbol=\"IF888.CFFEX\",\n", "    interval=\"1m\",\n", "    start=datetime(2019, 1, 1),\n", "    end=datetime(2019, 4, 30),\n", "    rate=0.3/10000,\n", "    slippage=0.2,\n", "    size=300,\n", "    pricetick=0.2,\n", "    capital=1_000_000,\n", ")\n", "engine.add_strategy(AtrRsiStrategy, {})"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-06-16 20:52:03.097314\t开始加载历史数据\n", "2025-06-16 20:52:03.097314\t加载进度：# [0%]\n", "2025-06-16 20:52:05.032964\t加载进度：# [9%]\n", "2025-06-16 20:52:05.103266\t加载进度：## [18%]\n", "2025-06-16 20:52:05.165507\t加载进度：### [28%]\n", "2025-06-16 20:52:05.225147\t加载进度：#### [37%]\n", "2025-06-16 20:52:05.293374\t加载进度：##### [46%]\n", "2025-06-16 20:52:05.428085\t加载进度：###### [55%]\n", "2025-06-16 20:52:05.494678\t加载进度：####### [65%]\n", "2025-06-16 20:52:05.561114\t加载进度：######## [74%]\n", "2025-06-16 20:52:05.621792\t加载进度：######### [83%]\n", "2025-06-16 20:52:05.687958\t加载进度：########## [92%]\n", "2025-06-16 20:52:05.751452\t历史数据加载完成，数据量：18960\n", "2025-06-16 20:52:05.840570\t策略初始化完成\n", "2025-06-16 20:52:05.840570\t开始回放历史数据\n", "2025-06-16 20:52:05.909744\t回放进度：= [0%]\n", "2025-06-16 20:52:05.979275\t回放进度：== [10%]\n", "2025-06-16 20:52:06.047814\t回放进度：=== [20%]\n", "2025-06-16 20:52:06.123807\t回放进度：==== [30%]\n", "2025-06-16 20:52:06.191131\t回放进度：===== [40%]\n", "2025-06-16 20:52:06.267813\t回放进度：====== [50%]\n", "2025-06-16 20:52:06.337686\t回放进度：======= [60%]\n", "2025-06-16 20:52:06.406952\t回放进度：======== [70%]\n", "2025-06-16 20:52:06.460610\t回放进度：========= [80%]\n", "2025-06-16 20:52:06.512522\t回放进度：========== [90%]\n", "2025-06-16 20:52:06.512522\t历史数据回放结束\n", "2025-06-16 20:52:06.513522\t开始计算逐日盯市盈亏\n", "2025-06-16 20:52:06.532825\t逐日盯市盈亏计算完成\n", "2025-06-16 20:52:06.532825\t开始计算策略统计指标\n", "2025-06-16 20:52:06.546318\t------------------------------\n", "2025-06-16 20:52:06.546318\t首个交易日：\t2019-01-02\n", "2025-06-16 20:52:06.546318\t最后交易日：\t2019-04-30\n", "2025-06-16 20:52:06.546318\t总交易日：\t79\n", "2025-06-16 20:52:06.546318\t盈利交易日：\t39\n", "2025-06-16 20:52:06.546318\t亏损交易日：\t40\n", "2025-06-16 20:52:06.546318\t起始资金：\t1,000,000.00\n", "2025-06-16 20:52:06.546318\t结束资金：\t1,051,703.36\n", "2025-06-16 20:52:06.546318\t总收益率：\t5.17%\n", "2025-06-16 20:52:06.546318\t年化收益：\t15.71%\n", "2025-06-16 20:52:06.546318\t最大回撤: \t-93,714.14\n", "2025-06-16 20:52:06.546318\t百分比最大回撤: -8.58%\n", "2025-06-16 20:52:06.546318\t最大回撤天数: \t30\n", "2025-06-16 20:52:06.546318\t总盈亏：\t51,703.36\n", "2025-06-16 20:52:06.546318\t总手续费：\t13,396.64\n", "2025-06-16 20:52:06.546318\t总滑点：\t29,940.00\n", "2025-06-16 20:52:06.546318\t总成交金额：\t446,554,620.00\n", "2025-06-16 20:52:06.546318\t总成交笔数：\t499\n", "2025-06-16 20:52:06.546318\t日均盈亏：\t654.47\n", "2025-06-16 20:52:06.546318\t日均手续费：\t169.58\n", "2025-06-16 20:52:06.546318\t日均滑点：\t378.99\n", "2025-06-16 20:52:06.546318\t日均成交金额：\t5,652,590.13\n", "2025-06-16 20:52:06.546318\t日均成交笔数：\t6.3164556962025316\n", "2025-06-16 20:52:06.546318\t日均收益率：\t0.06%\n", "2025-06-16 20:52:06.546318\t收益标准差：\t1.48%\n", "2025-06-16 20:52:06.546318\tSharpe Ratio：\t0.67\n", "2025-06-16 20:52:06.546318\tEWM Sharpe：\t0.59\n", "2025-06-16 20:52:06.546318\t收益回撤比：\t0.60\n", "2025-06-16 20:52:06.547317\t策略统计指标计算完成\n"]}, {"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"mode": "lines", "name": "Balance", "type": "scatter", "x": ["2019-01-02", "2019-01-03", "2019-01-04", "2019-01-07", "2019-01-08", "2019-01-09", "2019-01-10", "2019-01-11", "2019-01-14", "2019-01-15", "2019-01-16", "2019-01-17", "2019-01-18", "2019-01-21", "2019-01-22", "2019-01-23", "2019-01-24", "2019-01-25", "2019-01-28", "2019-01-29", "2019-01-30", "2019-01-31", "2019-02-01", "2019-02-11", "2019-02-12", "2019-02-13", "2019-02-14", "2019-02-15", "2019-02-18", "2019-02-19", "2019-02-20", "2019-02-21", "2019-02-22", "2019-02-25", "2019-02-26", "2019-02-27", "2019-02-28", "2019-03-01", "2019-03-04", "2019-03-05", "2019-03-06", "2019-03-07", "2019-03-08", "2019-03-11", "2019-03-12", "2019-03-13", "2019-03-14", "2019-03-15", "2019-03-18", "2019-03-19", "2019-03-20", "2019-03-21", "2019-03-22", "2019-03-25", "2019-03-26", "2019-03-27", "2019-03-28", "2019-03-29", "2019-04-01", "2019-04-02", "2019-04-03", "2019-04-04", "2019-04-08", "2019-04-09", "2019-04-10", "2019-04-11", "2019-04-12", "2019-04-15", "2019-04-16", "2019-04-17", "2019-04-18", "2019-04-19", "2019-04-22", "2019-04-23", "2019-04-24", "2019-04-25", "2019-04-26", "2019-04-29", "2019-04-30"], "xaxis": "x", "y": [1003796.0518000001, 999389.6998000002, 995342.0086, 987113.9818000001, 982931.4046, 985261.2214000002, 982894.7638, 980064.4888, 973241.341, 982738.0059999999, 974474.0967999999, 974881.6293999997, 977988.4744, 982263.3286, 994083.3286, 988654.27, 979461.151, 987575.9206, 990844.6065999998, 988369.4175999998, 978093.7443999999, 974177.3889999999, 986371.6545999999, 999631.6545999999, 1005871.6545999999, 1028431.6545999999, 1021853.5348, 1035424.5046, 1042826.9643999999, 1039411.9504000001, 1014537.7366, 1032727.369, 1046988.7642, 1091942.2336000002, 1076594.338, 1071707.0745999997, 1049716.2484, 1048904.6535999998, 1076370.6502, 1046785.7289999997, 1044523.3737999998, 1042688.4844, 1070256.0507999999, 1047724.7439999997, 1076293.8099999996, 1066599.8031999995, 1046766.5433999994, 1047746.3943999994, 1076671.0227999995, 1076074.0311999994, 1079983.9725999993, 1045206.0249999992, 1045516.3191999991, 1019557.6701999994, 1014587.1717999993, 998228.0973999992, 998538.6681999991, 1033223.6655999991, 1044866.1279999991, 1033767.3405999991, 1048828.113399999, 1050115.123599999, 1053080.205399999, 1058688.835599999, 1070776.2477999988, 1065163.7889999987, 1066574.3469999987, 1097351.7963999987, 1108151.2293999987, 1086126.4419999986, 1074423.3135999986, 1103999.6343999985, 1086686.3163999985, 1083656.0331999986, 1085936.5197999985, 1098536.7867999985, 1091164.3617999984, 1078935.5307999987, 1051703.3613999987], "yaxis": "y"}, {"fill": "tozer<PERSON>", "fillcolor": "red", "mode": "lines", "name": "Drawdown", "type": "scatter", "x": ["2019-01-02", "2019-01-03", "2019-01-04", "2019-01-07", "2019-01-08", "2019-01-09", "2019-01-10", "2019-01-11", "2019-01-14", "2019-01-15", "2019-01-16", "2019-01-17", "2019-01-18", "2019-01-21", "2019-01-22", "2019-01-23", "2019-01-24", "2019-01-25", "2019-01-28", "2019-01-29", "2019-01-30", "2019-01-31", "2019-02-01", "2019-02-11", "2019-02-12", "2019-02-13", "2019-02-14", "2019-02-15", "2019-02-18", "2019-02-19", "2019-02-20", "2019-02-21", "2019-02-22", "2019-02-25", "2019-02-26", "2019-02-27", "2019-02-28", "2019-03-01", "2019-03-04", "2019-03-05", "2019-03-06", "2019-03-07", "2019-03-08", "2019-03-11", "2019-03-12", "2019-03-13", "2019-03-14", "2019-03-15", "2019-03-18", "2019-03-19", "2019-03-20", "2019-03-21", "2019-03-22", "2019-03-25", "2019-03-26", "2019-03-27", "2019-03-28", "2019-03-29", "2019-04-01", "2019-04-02", "2019-04-03", "2019-04-04", "2019-04-08", "2019-04-09", "2019-04-10", "2019-04-11", "2019-04-12", "2019-04-15", "2019-04-16", "2019-04-17", "2019-04-18", "2019-04-19", "2019-04-22", "2019-04-23", "2019-04-24", "2019-04-25", "2019-04-26", "2019-04-29", "2019-04-30"], "xaxis": "x2", "y": [0, -4406.351999999955, -8454.04320000019, -16682.070000000065, -20864.647200000123, -18534.830399999977, -20901.288000000175, -23731.563000000082, -30554.710800000117, -21058.045800000196, -29321.95500000019, -28914.42240000039, -25807.577400000184, -21532.723200000124, -9712.723200000124, -15141.781800000113, -24334.900800000178, -16220.131200000178, -12951.445200000308, -15426.63420000032, -25702.30740000028, -29618.662800000282, -17424.39720000024, -4164.3972000002395, 0, 0, -6578.119799999869, 0, 0, -3415.01399999985, -28289.227799999993, -10099.595399999991, 0, 0, -15347.895600000164, -20235.15900000045, -42225.98520000023, -43037.58000000031, -15571.58340000012, -45156.50460000045, -47418.859800000326, -49253.749200000195, -21686.1828000003, -44217.48960000044, -15648.423600000562, -25342.430400000652, -45175.69020000077, -44195.839200000744, -15271.2108000007, -15868.202400000766, -11958.261000000872, -46736.208600000944, -46425.91440000106, -72384.5634000008, -77355.06180000084, -93714.136200001, -93403.56540000101, -58718.56800000102, -47076.10560000106, -58174.89300000109, -43114.12020000117, -41827.11000000127, -38862.02820000122, -33253.39800000121, -21165.985800001305, -26778.444600001443, -25367.886600001482, 0, 0, -22024.78740000003, -33727.915800000075, -4151.595000000205, -21464.913000000175, -24495.196200000122, -22214.709600000177, -9614.442600000184, -16986.86760000023, -29215.698600000003, -56447.86800000002], "yaxis": "y2"}, {"name": "Daily Pnl", "type": "bar", "xaxis": "x3", "y": [3796.0518000001366, -4406.352, -4047.6912000001894, -8228.026799999918, -4182.577200000027, 2329.8168000001638, -2366.457600000191, -2830.2749999999182, -6823.147800000082, 9496.664999999946, -8263.9092, 407.5325999998636, 3106.8450000001367, 4274.854200000054, 11820.000000000027, -5429.0586, -9193.119, 8114.769599999972, 3268.6859999998637, -2475.1889999999726, -10275.673199999972, -3916.355400000027, 12194.265600000082, 13259.999999999945, 6240.000000000055, 22559.999999999945, -6578.1197999999185, 13570.969800000055, 7402.45979999992, -3415.013999999891, -24874.213800000052, 18189.632399999948, 14261.395200000059, 44953.46940000012, -15347.895600000224, -4887.263400000187, -21990.826199999894, -811.59479999992, 27465.99660000008, -29584.921200000324, -2262.3551999998344, -1834.8893999998636, 27567.566399999778, -22531.30680000008, 28569.06599999989, -9694.006800000026, -19833.259800000087, 979.850999999971, 28924.6284, -596.9916000000273, 3909.941399999918, -34777.94760000006, 310.2941999999182, -25958.648999999838, -4970.498400000024, -16359.074400000081, 310.5707999999454, 34684.99739999994, 11642.462399999975, -11098.787400000054, 15060.77279999992, 1287.0102, 2965.0818, 5608.630199999972, 12087.412199999919, -5612.4588, 1410.5579999999727, 30777.449399999892, 10799.433000000066, -22024.78740000014, -11703.128400000081, 29576.320800000027, -17313.318000000058, -3030.2832, 2280.4866, 12600.26699999997, -7372.42500000008, -12228.830999999807, -27232.169400000028], "yaxis": "y3"}, {"name": "Days", "nbinsx": 100, "type": "histogram", "x": [3796.0518000001366, -4406.352, -4047.6912000001894, -8228.026799999918, -4182.577200000027, 2329.8168000001638, -2366.457600000191, -2830.2749999999182, -6823.147800000082, 9496.664999999946, -8263.9092, 407.5325999998636, 3106.8450000001367, 4274.854200000054, 11820.000000000027, -5429.0586, -9193.119, 8114.769599999972, 3268.6859999998637, -2475.1889999999726, -10275.673199999972, -3916.355400000027, 12194.265600000082, 13259.999999999945, 6240.000000000055, 22559.999999999945, -6578.1197999999185, 13570.969800000055, 7402.45979999992, -3415.013999999891, -24874.213800000052, 18189.632399999948, 14261.395200000059, 44953.46940000012, -15347.895600000224, -4887.263400000187, -21990.826199999894, -811.59479999992, 27465.99660000008, -29584.921200000324, -2262.3551999998344, -1834.8893999998636, 27567.566399999778, -22531.30680000008, 28569.06599999989, -9694.006800000026, -19833.259800000087, 979.850999999971, 28924.6284, -596.9916000000273, 3909.941399999918, -34777.94760000006, 310.2941999999182, -25958.648999999838, -4970.498400000024, -16359.074400000081, 310.5707999999454, 34684.99739999994, 11642.462399999975, -11098.787400000054, 15060.77279999992, 1287.0102, 2965.0818, 5608.630199999972, 12087.412199999919, -5612.4588, 1410.5579999999727, 30777.449399999892, 10799.433000000066, -22024.78740000014, -11703.128400000081, 29576.320800000027, -17313.318000000058, -3030.2832, 2280.4866, 12600.26699999997, -7372.42500000008, -12228.830999999807, -27232.169400000028], "xaxis": "x4", "yaxis": "y4"}], "layout": {"annotations": [{"font": {"size": 16}, "showarrow": false, "text": "Balance", "x": 0.5, "xanchor": "center", "xref": "paper", "y": 1, "yanchor": "bottom", "yref": "paper"}, {"font": {"size": 16}, "showarrow": false, "text": "Drawdown", "x": 0.5, "xanchor": "center", "xref": "paper", "y": 0.7350000000000001, "yanchor": "bottom", "yref": "paper"}, {"font": {"size": 16}, "showarrow": false, "text": "Daily Pnl", "x": 0.5, "xanchor": "center", "xref": "paper", "y": 0.47000000000000003, "yanchor": "bottom", "yref": "paper"}, {"font": {"size": 16}, "showarrow": false, "text": "Pnl Distribution", "x": 0.5, "xanchor": "center", "xref": "paper", "y": 0.20500000000000002, "yanchor": "bottom", "yref": "paper"}], "height": 1000, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 1000, "xaxis": {"anchor": "y", "domain": [0, 1]}, "xaxis2": {"anchor": "y2", "domain": [0, 1]}, "xaxis3": {"anchor": "y3", "domain": [0, 1]}, "xaxis4": {"anchor": "y4", "domain": [0, 1]}, "yaxis": {"anchor": "x", "domain": [0.7949999999999999, 1]}, "yaxis2": {"anchor": "x2", "domain": [0.53, 0.7350000000000001]}, "yaxis3": {"anchor": "x3", "domain": [0.265, 0.47000000000000003]}, "yaxis4": {"anchor": "x4", "domain": [0, 0.20500000000000002]}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["engine.load_data()\n", "engine.run_backtesting()\n", "df = engine.calculate_result()\n", "engine.calculate_statistics()\n", "engine.show_chart()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-06-16 20:52:12.736299\t开始执行遗传算法优化\n", "2025-06-16 20:52:12.737296\t参数优化空间：9\n", "2025-06-16 20:52:12.737296\t每代族群总数：100\n", "2025-06-16 20:52:12.737296\t优良筛选个数：80\n", "2025-06-16 20:52:12.737296\t迭代次数：30\n", "2025-06-16 20:52:12.737296\t交叉概率：95%\n", "2025-06-16 20:52:12.737296\t突变概率：5%\n", "2025-06-16 20:52:12.737296\t个体突变概率：100%\n", "gen\tnevals\n", "0  \t100   \n", "1  \t100   \n", "2  \t100   \n", "3  \t100   \n", "4  \t100   \n", "5  \t100   \n", "6  \t100   \n", "7  \t100   \n", "8  \t100   \n", "9  \t100   \n", "10 \t100   \n", "11 \t100   \n", "12 \t100   \n", "13 \t100   \n", "14 \t100   \n", "15 \t100   \n", "16 \t100   \n", "17 \t100   \n", "18 \t100   \n", "19 \t100   \n", "20 \t100   \n", "21 \t100   \n", "22 \t100   \n", "23 \t100   \n", "24 \t100   \n", "25 \t100   \n", "26 \t100   \n", "27 \t100   \n", "28 \t100   \n", "29 \t100   \n", "30 \t100   \n", "2025-06-16 20:52:26.338670\t遗传算法优化完成，耗时13秒\n", "2025-06-16 20:52:26.462433\t参数：{'atr_length': 25, 'atr_ma_length': 20}, 目标：2.0127186461733073\n", "2025-06-16 20:52:26.463435\t参数：{'atr_length': 27, 'atr_ma_length': 20}, 目标：1.972733364131168\n", "2025-06-16 20:52:26.463435\t参数：{'atr_length': 26, 'atr_ma_length': 20}, 目标：1.927735865279324\n", "2025-06-16 20:52:26.463435\t参数：{'atr_length': 27, 'atr_ma_length': 30}, 目标：1.273993651024933\n", "2025-06-16 20:52:26.463435\t参数：{'atr_length': 25, 'atr_ma_length': 30}, 目标：1.220296323588909\n", "2025-06-16 20:52:26.463435\t参数：{'atr_length': 26, 'atr_ma_length': 30}, 目标：1.100003635114256\n", "2025-06-16 20:52:26.463435\t参数：{'atr_length': 27, 'atr_ma_length': 10}, 目标：0.7261753437654116\n", "2025-06-16 20:52:26.463435\t参数：{'atr_length': 26, 'atr_ma_length': 10}, 目标：0.5706887229122534\n", "2025-06-16 20:52:26.463435\t参数：{'atr_length': 25, 'atr_ma_length': 10}, 目标：0.46478426808123563\n"]}, {"data": {"text/plain": ["[({'atr_length': 25, 'atr_ma_length': 20},\n", "  2.0127186461733073,\n", "  {'start_date': datetime.date(2019, 1, 2),\n", "   'end_date': datetime.date(2019, 4, 30),\n", "   'total_days': 79,\n", "   'profit_days': 38,\n", "   'loss_days': 41,\n", "   'capital': 1000000,\n", "   'end_balance': 1157730.3249999983,\n", "   'max_drawdown': -56901.58860000118,\n", "   'max_ddpercent': -5.230378359138127,\n", "   'max_drawdown_duration': 31,\n", "   'total_net_pnl': 157730.32499999838,\n", "   'daily_net_pnl': 1996.5863924050427,\n", "   'total_commission': 12249.674999999997,\n", "   'daily_commission': 155.05917721518983,\n", "   'total_slippage': 27540.0,\n", "   'daily_slippage': 348.60759493670884,\n", "   'total_turnover': 408322500.0,\n", "   'daily_turnover': 5168639.24050633,\n", "   'total_trade_count': 459,\n", "   'daily_trade_count': 5.810126582278481,\n", "   'total_return': 15.77303249999984,\n", "   'annual_return': 47.91807341772103,\n", "   'daily_return': 0.1853942684361968,\n", "   'return_std': 1.4269831811830316,\n", "   'sharpe_ratio': 2.0127186461733073,\n", "   'ewm_sharpe': 2.0847989170594987,\n", "   'return_drawdown_ratio': 3.015658030253657}),\n", " ({'atr_length': 27, 'atr_ma_length': 20},\n", "  1.972733364131168,\n", "  {'start_date': datetime.date(2019, 1, 2),\n", "   'end_date': datetime.date(2019, 4, 30),\n", "   'total_days': 79,\n", "   'profit_days': 40,\n", "   'loss_days': 39,\n", "   'capital': 1000000,\n", "   'end_balance': 1152267.1455999983,\n", "   'max_drawdown': -49582.891800000914,\n", "   'max_ddpercent': -4.569238197913546,\n", "   'max_drawdown_duration': 31,\n", "   'total_net_pnl': 152267.1455999984,\n", "   'daily_net_pnl': 1927.4322227847897,\n", "   'total_commission': 12372.854399999998,\n", "   'daily_commission': 156.61841012658226,\n", "   'total_slippage': 27780.0,\n", "   'daily_slippage': 351.6455696202532,\n", "   'total_turnover': 412428480.0,\n", "   'daily_turnover': 5220613.670886076,\n", "   'total_trade_count': 463,\n", "   'daily_trade_count': 5.860759493670886,\n", "   'total_return': 15.22671455999982,\n", "   'annual_return': 46.258373346834894,\n", "   'daily_return': 0.17940687672394376,\n", "   'return_std': 1.4088875027526955,\n", "   'sharpe_ratio': 1.972733364131168,\n", "   'ewm_sharpe': 2.026542024055209,\n", "   'return_drawdown_ratio': 3.332440529572917}),\n", " ({'atr_length': 26, 'atr_ma_length': 20},\n", "  1.927735865279324,\n", "  {'start_date': datetime.date(2019, 1, 2),\n", "   'end_date': datetime.date(2019, 4, 30),\n", "   'total_days': 79,\n", "   'profit_days': 39,\n", "   'loss_days': 40,\n", "   'capital': 1000000,\n", "   'end_balance': 1147643.3271999985,\n", "   'max_drawdown': -50666.8596000009,\n", "   'max_ddpercent': -4.666033619086949,\n", "   'max_drawdown_duration': 31,\n", "   'total_net_pnl': 147643.32719999852,\n", "   'daily_net_pnl': 1868.9028759493483,\n", "   'total_commission': 12316.672799999998,\n", "   'daily_commission': 155.90725063291137,\n", "   'total_slippage': 27660.0,\n", "   'daily_slippage': 350.126582278481,\n", "   'total_turnover': 410555760.0,\n", "   'daily_turnover': 5196908.354430379,\n", "   'total_trade_count': 461,\n", "   'daily_trade_count': 5.8354430379746836,\n", "   'total_return': 14.764332719999862,\n", "   'annual_return': 44.85366902278439,\n", "   'daily_return': 0.17431716333002323,\n", "   'return_std': 1.4008713178918295,\n", "   'sharpe_ratio': 1.927735865279324,\n", "   'ewm_sharpe': 1.9723836768576808,\n", "   'return_drawdown_ratio': 3.164214818256914}),\n", " ({'atr_length': 27, 'atr_ma_length': 30},\n", "  1.273993651024933,\n", "  {'start_date': datetime.date(2019, 1, 2),\n", "   'end_date': datetime.date(2019, 4, 30),\n", "   'total_days': 79,\n", "   'profit_days': 38,\n", "   'loss_days': 41,\n", "   'capital': 1000000,\n", "   'end_balance': 1094503.6569999983,\n", "   'max_drawdown': -77433.63960000011,\n", "   'max_ddpercent': -6.607319335654653,\n", "   'max_drawdown_duration': 11,\n", "   'total_net_pnl': 94503.65699999814,\n", "   'daily_net_pnl': 1196.2488227847866,\n", "   'total_commission': 12356.342999999999,\n", "   'daily_commission': 156.40940506329113,\n", "   'total_slippage': 27660.0,\n", "   'daily_slippage': 350.126582278481,\n", "   'total_turnover': 411878100.0,\n", "   'daily_turnover': 5213646.835443038,\n", "   'total_trade_count': 461,\n", "   'daily_trade_count': 5.8354430379746836,\n", "   'total_return': 9.450365699999818,\n", "   'annual_return': 28.70997174683489,\n", "   'daily_return': 0.11430503699608949,\n", "   'return_std': 1.3899645553722253,\n", "   'sharpe_ratio': 1.273993651024933,\n", "   'ewm_sharpe': 1.1696997763268426,\n", "   'return_drawdown_ratio': 1.4302874161088925}),\n", " ({'atr_length': 25, 'atr_ma_length': 30},\n", "  1.220296323588909,\n", "  {'start_date': datetime.date(2019, 1, 2),\n", "   'end_date': datetime.date(2019, 4, 30),\n", "   'total_days': 79,\n", "   'profit_days': 36,\n", "   'loss_days': 43,\n", "   'capital': 1000000,\n", "   'end_balance': 1094928.4857999983,\n", "   'max_drawdown': -76113.6000000001,\n", "   'max_ddpercent': -6.499646846424227,\n", "   'max_drawdown_duration': 11,\n", "   'total_net_pnl': 94928.48579999819,\n", "   'daily_net_pnl': 1201.6264025316227,\n", "   'total_commission': 12291.5142,\n", "   'daily_commission': 155.58878734177216,\n", "   'total_slippage': 27540.0,\n", "   'daily_slippage': 348.60759493670884,\n", "   'total_turnover': 409717140.0,\n", "   'daily_turnover': 5186292.911392405,\n", "   'total_trade_count': 459,\n", "   'daily_trade_count': 5.810126582278481,\n", "   'total_return': 9.492848579999835,\n", "   'annual_return': 28.839033660758993,\n", "   'daily_return': 0.11479626754936274,\n", "   'return_std': 1.4573641625596836,\n", "   'sharpe_ratio': 1.220296323588909,\n", "   'ewm_sharpe': 1.153717453838036,\n", "   'return_drawdown_ratio': 1.4605175949247635}),\n", " ({'atr_length': 26, 'atr_ma_length': 30},\n", "  1.100003635114256,\n", "  {'start_date': datetime.date(2019, 1, 2),\n", "   'end_date': datetime.date(2019, 4, 30),\n", "   'total_days': 79,\n", "   'profit_days': 37,\n", "   'loss_days': 42,\n", "   'capital': 1000000,\n", "   'end_balance': 1082878.4175999982,\n", "   'max_drawdown': -76113.6000000001,\n", "   'max_ddpercent': -6.567223832793394,\n", "   'max_drawdown_duration': 11,\n", "   'total_net_pnl': 82878.4175999981,\n", "   'daily_net_pnl': 1049.093893670862,\n", "   'total_commission': 12401.5824,\n", "   'daily_commission': 156.98205569620254,\n", "   'total_slippage': 27780.0,\n", "   'daily_slippage': 351.6455696202532,\n", "   'total_turnover': 413386080.0,\n", "   'daily_turnover': 5232735.189873418,\n", "   'total_trade_count': 463,\n", "   'daily_trade_count': 5.860759493670886,\n", "   'total_return': 8.287841759999814,\n", "   'annual_return': 25.1782534481007,\n", "   'daily_return': 0.10078822438750101,\n", "   'return_std': 1.4194539075539052,\n", "   'sharpe_ratio': 1.100003635114256,\n", "   'ewm_sharpe': 1.025155295553051,\n", "   'return_drawdown_ratio': 1.2620008044517266}),\n", " ({'atr_length': 27, 'atr_ma_length': 10},\n", "  0.7261753437654116,\n", "  {'start_date': datetime.date(2019, 1, 2),\n", "   'end_date': datetime.date(2019, 4, 30),\n", "   'total_days': 79,\n", "   'profit_days': 39,\n", "   'loss_days': 40,\n", "   'capital': 1000000,\n", "   'end_balance': 1057831.2345999987,\n", "   'max_drawdown': -90713.4234000009,\n", "   'max_ddpercent': -8.259369529539693,\n", "   'max_drawdown_duration': 30,\n", "   'total_net_pnl': 57831.23459999873,\n", "   'daily_net_pnl': 732.0409443037814,\n", "   'total_commission': 13388.765399999998,\n", "   'daily_commission': 169.47804303797466,\n", "   'total_slippage': 29940.0,\n", "   'daily_slippage': 378.9873417721519,\n", "   'total_turnover': 446292180.0,\n", "   'daily_turnover': 5649268.101265823,\n", "   'total_trade_count': 499,\n", "   'daily_trade_count': 6.3164556962025316,\n", "   'total_return': 5.783123459999873,\n", "   'annual_return': 17.56898266329075,\n", "   'daily_return': 0.07116557860996443,\n", "   'return_std': 1.5182178967984687,\n", "   'sharpe_ratio': 0.7261753437654116,\n", "   'ewm_sharpe': 0.6534432099800027,\n", "   'return_drawdown_ratio': 0.7001894562673932}),\n", " ({'atr_length': 26, 'atr_ma_length': 10},\n", "  0.5706887229122534,\n", "  {'start_date': datetime.date(2019, 1, 2),\n", "   'end_date': datetime.date(2019, 4, 30),\n", "   'total_days': 79,\n", "   'profit_days': 39,\n", "   'loss_days': 40,\n", "   'capital': 1000000,\n", "   'end_balance': 1045896.9177999987,\n", "   'max_drawdown': -102407.73300000094,\n", "   'max_ddpercent': -9.32616310918827,\n", "   'max_drawdown_duration': 30,\n", "   'total_net_pnl': 45896.91779999864,\n", "   'daily_net_pnl': 580.9736430379575,\n", "   'total_commission': 13443.082199999999,\n", "   'daily_commission': 170.1655974683544,\n", "   'total_slippage': 30060.0,\n", "   'daily_slippage': 380.50632911392404,\n", "   'total_turnover': 448102740.0,\n", "   'daily_turnover': 5672186.582278481,\n", "   'total_trade_count': 501,\n", "   'daily_trade_count': 6.341772151898734,\n", "   'total_return': 4.58969177999986,\n", "   'annual_return': 13.943367432910968,\n", "   'daily_return': 0.05680355928871988,\n", "   'return_std': 1.5419911436683065,\n", "   'sharpe_ratio': 0.5706887229122534,\n", "   'ewm_sharpe': 0.504537957914293,\n", "   'return_drawdown_ratio': 0.49213076441672243}),\n", " ({'atr_length': 25, 'atr_ma_length': 10},\n", "  0.46478426808123563,\n", "  {'start_date': datetime.date(2019, 1, 2),\n", "   'end_date': datetime.date(2019, 4, 30),\n", "   'total_days': 79,\n", "   'profit_days': 39,\n", "   'loss_days': 40,\n", "   'capital': 1000000,\n", "   'end_balance': 1037256.6585999986,\n", "   'max_drawdown': -110927.99580000085,\n", "   'max_ddpercent': -10.103198434300957,\n", "   'max_drawdown_duration': 30,\n", "   'total_net_pnl': 37256.65859999858,\n", "   'daily_net_pnl': 471.6032734177035,\n", "   'total_commission': 13443.341399999998,\n", "   'daily_commission': 170.16887848101263,\n", "   'total_slippage': 30060.0,\n", "   'daily_slippage': 380.50632911392404,\n", "   'total_turnover': 448111380.0,\n", "   'daily_turnover': 5672295.949367088,\n", "   'total_trade_count': 501,\n", "   'daily_trade_count': 6.341772151898734,\n", "   'total_return': 3.7256658599998493,\n", "   'annual_return': 11.31847856202486,\n", "   'daily_return': 0.046303037575705135,\n", "   'return_std': 1.5433473611303774,\n", "   'sharpe_ratio': 0.46478426808123563,\n", "   'ewm_sharpe': 0.39342110236894756,\n", "   'return_drawdown_ratio': 0.36876102990821136})]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["setting = OptimizationSetting()\n", "setting.set_target(\"sharpe_ratio\")\n", "setting.add_parameter(\"atr_length\", 25, 27, 1)\n", "setting.add_parameter(\"atr_ma_length\", 10, 30, 10)\n", "\n", "engine.run_ga_optimization(setting)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": false}, "outputs": [], "source": ["engine.run_bf_optimization(setting)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.2"}}, "nbformat": 4, "nbformat_minor": 2}