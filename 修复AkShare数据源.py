#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复AkShare数据源配置问题
解决vnpy_akshare模块中类名不匹配的问题
"""

import json
from pathlib import Path

def check_akshare_module():
    """检查AkShare模块的正确导入方式"""
    print("=== AkShare模块检查 ===")
    
    try:
        # 检查vnpy_akshare模块内容
        import vnpy_akshare
        print(f"✅ vnpy_akshare模块版本: {vnpy_akshare.__version__}")
        print(f"模块内容: {dir(vnpy_akshare)}")
        
        # 尝试正确的导入方式
        from vnpy_akshare import Datafeed
        print("✅ Datafeed类导入成功")
        
        # 检查类的属性
        print(f"Datafeed类: {Datafeed}")
        print(f"Datafeed类属性: {dir(Datafeed)}")
        
        return True, Datafeed
        
    except Exception as e:
        print(f"❌ AkShare模块检查失败: {e}")
        return False, None

def test_datafeed_creation():
    """测试数据源创建"""
    print("\n=== 数据源创建测试 ===")
    
    try:
        from vnpy_akshare import Datafeed
        
        # 创建数据源实例
        datafeed = Datafeed()
        print("✅ AkShare数据源实例创建成功")
        
        # 检查数据源方法
        methods = [method for method in dir(datafeed) if not method.startswith('_')]
        print(f"数据源可用方法: {methods}")
        
        return True, datafeed
        
    except Exception as e:
        print(f"❌ 数据源创建失败: {e}")
        return False, None

def test_data_query():
    """测试数据查询功能"""
    print("\n=== 数据查询测试 ===")
    
    try:
        from vnpy_akshare import Datafeed
        from vnpy.trader.object import HistoryRequest
        from vnpy.trader.constant import Exchange, Interval
        from datetime import datetime, timedelta
        
        # 创建数据源
        datafeed = Datafeed()
        
        # 初始化数据源
        datafeed.init()
        print("✅ 数据源初始化成功")
        
        # 创建历史数据请求
        req = HistoryRequest(
            symbol="000001",
            exchange=Exchange.SZSE,
            interval=Interval.DAILY,
            start=datetime.now() - timedelta(days=10),
            end=datetime.now()
        )
        
        print("正在查询历史数据...")
        bars = datafeed.query_bar_history(req)
        
        if bars:
            print(f"✅ 历史数据查询成功，获取到 {len(bars)} 条数据")
            if len(bars) > 0:
                print(f"最新数据: {bars[-1].datetime} - 收盘价: {bars[-1].close_price}")
            return True
        else:
            print("❌ 历史数据查询返回空结果")
            return False
            
    except Exception as e:
        print(f"❌ 数据查询测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_vnpy_datafeed_registration():
    """检查VeighNa数据源注册"""
    print("\n=== VeighNa数据源注册检查 ===")
    
    try:
        from vnpy.trader.datafeed import get_datafeed
        
        # 获取当前注册的数据源
        datafeed = get_datafeed()
        
        if datafeed:
            print(f"✅ 当前数据源: {datafeed.__class__.__name__}")
            print(f"数据源模块: {datafeed.__class__.__module__}")
            return True
        else:
            print("❌ 未找到注册的数据源")
            return False
            
    except Exception as e:
        print(f"❌ 数据源注册检查失败: {e}")
        return False

def reinstall_akshare():
    """重新安装AkShare相关模块"""
    print("\n=== 重新安装AkShare模块 ===")
    
    import subprocess
    import sys
    
    try:
        print("正在重新安装vnpy_akshare...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "vnpy_akshare"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ vnpy_akshare重新安装成功")
            print(result.stdout)
        else:
            print("❌ vnpy_akshare重新安装失败")
            print(result.stderr)
            
        print("\n正在重新安装akshare...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "akshare"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ akshare重新安装成功")
        else:
            print("❌ akshare重新安装失败")
            print(result.stderr)
            
    except Exception as e:
        print(f"❌ 重新安装失败: {e}")

def provide_final_solution():
    """提供最终解决方案"""
    print("\n=== 最终解决方案 ===")
    
    print("🎯 根据诊断结果，问题的根本原因是:")
    print("1. vnpy_akshare模块中的类名是'Datafeed'而不是'AkshareDatafeed'")
    print("2. VeighNa可能需要重新启动以正确加载数据源")
    print("")
    print("🔧 解决步骤:")
    print("")
    print("步骤1: 完全关闭VeighNa程序")
    print("步骤2: 重新运行启动脚本")
    print("   python start_vnpy_a_stock.py")
    print("")
    print("步骤3: 在VeighNa中重新尝试下载数据")
    print("   - 代码: 000001")
    print("   - 交易所: SZSE")
    print("   - 周期: DAILY")
    print("   - 开始日期: 2025/07/22")
    print("")
    print("步骤4: 如果仍然失败，检查VeighNa日志")
    print("   - 查看界面底部的日志输出")
    print("   - 寻找具体的错误信息")
    print("")
    print("💡 额外提示:")
    print("- 确保网络连接正常")
    print("- 尝试不同的股票代码")
    print("- 如果问题持续，可以尝试重新安装vnpy_akshare模块")

def main():
    """主函数"""
    print("AkShare数据源修复工具")
    print("=" * 50)
    
    # 检查模块
    module_ok, datafeed_class = check_akshare_module()
    
    if module_ok:
        # 测试数据源创建
        creation_ok, datafeed_instance = test_datafeed_creation()
        
        if creation_ok:
            # 测试数据查询
            query_ok = test_data_query()
        else:
            query_ok = False
    else:
        creation_ok = False
        query_ok = False
    
    # 检查VeighNa数据源注册
    registration_ok = check_vnpy_datafeed_registration()
    
    print("\n=== 诊断结果 ===")
    print(f"模块导入: {'✅' if module_ok else '❌'}")
    print(f"数据源创建: {'✅' if creation_ok else '❌'}")
    print(f"数据查询: {'✅' if query_ok else '❌'}")
    print(f"VeighNa注册: {'✅' if registration_ok else '❌'}")
    
    if all([module_ok, creation_ok, query_ok, registration_ok]):
        print("\n🎉 AkShare数据源工作正常！")
        print("如果VeighNa中仍然无法下载数据，请重启VeighNa程序。")
    else:
        print("\n⚠️  发现问题，建议重新安装相关模块。")
        
        # 询问是否重新安装
        print("\n是否要重新安装AkShare相关模块？(这可能需要几分钟)")
        # reinstall_akshare()  # 注释掉自动安装，让用户手动决定
    
    # 提供最终解决方案
    provide_final_solution()

if __name__ == "__main__":
    main()