#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据管理器界面详细指南
基于用户实际看到的数据管理器界面提供精确指导
"""

def analyze_data_manager_interface():
    """分析用户看到的数据管理器界面"""
    print("🔍 数据管理器界面分析")
    print("=" * 60)
    
    print("\n📋 根据您的截图，数据管理器界面包含：")
    print("-" * 50)
    print("")
    print("顶部工具栏：")
    print("  ├ 合约代码")
    print("  ├ 主力合约")
    print("  ├ 配置")
    print("  ├ 交易所")
    print("  ├ 数据类型")
    print("  ├ 开始时间")
    print("  ├ 结束时间")
    print("  └ 其他控制按钮")
    print("")
    print("左侧面板：")
    print("  ├ 合约代码")
    print("  ├ 内部代码")
    print("  └ 日志")
    print("")
    print("主要显示区域：")
    print("  └ 数据表格显示区域")
    
    print("\n🎯 寻找输入框的具体位置")
    print("-" * 40)
    print("")
    print("根据界面布局，股票代码输入框应该在：")
    print("")
    print("位置1: 顶部工具栏的 '合约代码' 区域")
    print("  ┌─────────────────────────────────────┐")
    print("  │ 合约代码  主力合约  配置  交易所  ... │ ← 这一行")
    print("  └─────────────────────────────────────┘")
    print("  在 '合约代码' 下方应该有一个输入框")
    print("")
    print("位置2: 左侧面板的 '合约代码' 区域")
    print("  ┌─────────────┐")
    print("  │ 合约代码    │ ← 这里")
    print("  │ [_________] │ ← 可能有输入框")
    print("  │             │")
    print("  │ 内部代码    │")
    print("  │ [_________] │")
    print("  └─────────────┘")
    
    print("\n🔍 详细查找步骤")
    print("-" * 30)
    print("")
    print("步骤1: 检查顶部工具栏")
    print("  1️⃣ 仔细查看 '合约代码' 标签下方")
    print("  2️⃣ 寻找一个文本输入框或下拉框")
    print("  3️⃣ 输入框可能显示为空白或有默认值")
    print("")
    print("步骤2: 检查左侧面板")
    print("  1️⃣ 在左侧 '合约代码' 区域寻找输入框")
    print("  2️⃣ 可能需要点击该区域激活输入")
    print("  3️⃣ 检查是否有搜索图标或按钮")
    print("")
    print("步骤3: 检查右键菜单")
    print("  1️⃣ 在主显示区域右键点击")
    print("  2️⃣ 查看是否有 '添加合约' 或 '查询' 选项")
    print("")
    print("步骤4: 检查隐藏的输入区域")
    print("  1️⃣ 尝试双击 '合约代码' 标签")
    print("  2️⃣ 查看是否有可展开的面板")
    print("  3️⃣ 检查窗口底部是否有输入区域")

def show_input_methods():
    """显示可能的输入方法"""
    print("\n💡 可能的输入方法")
    print("=" * 40)
    
    print("\n方法1: 直接输入")
    print("-" * 20)
    print("  ✓ 找到输入框后直接输入: 000001")
    print("  ✓ 按回车键确认")
    print("  ✓ 或点击查询/确认按钮")
    
    print("\n方法2: 下拉选择")
    print("-" * 20)
    print("  ✓ 如果是下拉框，点击下拉箭头")
    print("  ✓ 在列表中寻找 000001 或相关股票")
    print("  ✓ 点击选择")
    
    print("\n方法3: 搜索功能")
    print("-" * 20)
    print("  ✓ 寻找搜索图标 🔍")
    print("  ✓ 点击后输入股票代码")
    print("  ✓ 从搜索结果中选择")
    
    print("\n方法4: 添加合约")
    print("-" * 20)
    print("  ✓ 寻找 '添加' 或 '+' 按钮")
    print("  ✓ 点击后在弹出窗口中输入")
    print("  ✓ 设置交易所为 SZSE")

def show_troubleshooting():
    """显示故障排除方法"""
    print("\n🛠️  如果仍然找不到输入框")
    print("=" * 40)
    
    print("\n问题1: 界面元素被隐藏")
    print("-" * 30)
    print("解决方案:")
    print("  1. 尝试最大化数据管理器窗口")
    print("  2. 拖拽窗口边缘调整大小")
    print("  3. 检查是否有滚动条，尝试滚动")
    print("  4. 查看窗口顶部是否有隐藏的工具栏")
    
    print("\n问题2: 需要先连接数据源")
    print("-" * 30)
    print("解决方案:")
    print("  1. 检查数据源连接状态")
    print("  2. 确保AkShare数据源已激活")
    print("  3. 重启VeighNa重新连接")
    
    print("\n问题3: 界面版本差异")
    print("-" * 30)
    print("解决方案:")
    print("  1. 尝试不同的菜单路径")
    print("  2. 查看帮助文档或说明")
    print("  3. 使用K线图表作为替代方案")
    
    print("\n🎯 替代方案: 使用K线图表")
    print("-" * 30)
    print("如果数据管理器确实没有输入框：")
    print("")
    print("1️⃣ 关闭数据管理器")
    print("2️⃣ 在主菜单中寻找 'K线图表' 或 '图表分析'")
    print("3️⃣ 在K线图表中输入: 000001.SZSE")
    print("4️⃣ 这样也能查看股票数据")

def show_specific_instructions():
    """显示针对当前界面的具体指令"""
    print("\n📋 针对您当前界面的具体操作")
    print("=" * 50)
    
    print("\n🎯 立即尝试的操作:")
    print("-" * 30)
    
    print("操作1: 检查顶部 '合约代码' 区域")
    print("  ↓ 在 '合约代码' 标签正下方寻找输入框")
    print("  ↓ 可能是一个白色的文本框")
    print("  ↓ 尝试点击该区域激活输入")
    
    print("\n操作2: 检查左侧面板")
    print("  ↓ 在左侧 '合约代码' 区域点击")
    print("  ↓ 查看是否出现输入框")
    print("  ↓ 或者是否有可编辑的字段")
    
    print("\n操作3: 尝试键盘输入")
    print("  ↓ 直接在数据管理器界面按键盘")
    print("  ↓ 输入 000001")
    print("  ↓ 看是否有响应或弹出输入框")
    
    print("\n操作4: 查找工具按钮")
    print("  ↓ 寻找工具栏中的按钮")
    print("  ↓ 特别注意 🔍 搜索图标")
    print("  ↓ 或者 + 添加按钮")
    
    print("\n🔍 如果以上都没有找到")
    print("-" * 30)
    print("请尝试以下操作：")
    print("")
    print("1. 截图发送当前数据管理器的完整界面")
    print("2. 或者直接使用K线图表功能")
    print("3. 在主菜单 → 功能 → K线图表")
    print("4. 输入: 000001.SZSE 查看数据")

def check_data_status():
    """检查数据状态"""
    print("\n✅ 数据状态确认")
    print("=" * 30)
    
    import os
    import sqlite3
    
    vnpy_db = "f:/vn/database.db"
    
    if os.path.exists(vnpy_db):
        try:
            conn = sqlite3.connect(vnpy_db)
            cursor = conn.cursor()
            
            # 检查数据
            cursor.execute("SELECT COUNT(*) FROM dbbardata")
            count = cursor.fetchone()[0]
            
            print(f"📊 数据库中有 {count:,} 条数据记录")
            
            if count > 0:
                # 显示可用股票
                cursor.execute("SELECT DISTINCT symbol, exchange FROM dbbardata ORDER BY symbol LIMIT 5")
                stocks = cursor.fetchall()
                
                print("\n📋 可查询的股票代码:")
                for symbol, exchange in stocks:
                    print(f"   ✓ {symbol}.{exchange}")
                
                print("\n💡 数据已准备就绪，问题在于找到输入界面")
            else:
                print("⚠️  数据库为空，需要重新导入")
            
            conn.close()
        except Exception as e:
            print(f"❌ 数据检查失败: {e}")
    else:
        print("❌ 数据库文件不存在")

def main():
    """主函数"""
    print("🎯 数据管理器输入位置详细指南")
    print("=" * 60)
    print("专门解决'找不到输入股票代码的地方'的问题")
    print("")
    
    analyze_data_manager_interface()
    show_input_methods()
    show_troubleshooting()
    show_specific_instructions()
    check_data_status()
    
    print("\n🎉 总结")
    print("=" * 30)
    print("")
    print("🔍 重点检查位置:")
    print("   1. 顶部工具栏 '合约代码' 下方")
    print("   2. 左侧面板 '合约代码' 区域")
    print("   3. 寻找搜索图标或添加按钮")
    print("")
    print("🎯 如果找不到，使用K线图表替代:")
    print("   功能 → K线图表 → 输入 000001.SZSE")
    print("")
    print("📊 数据已准备就绪，关键是找到正确的输入界面！")

if __name__ == "__main__":
    main()