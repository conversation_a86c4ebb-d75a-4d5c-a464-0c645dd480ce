#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
根据用户实际VeighNa界面的数据查看指南
针对用户截图中的界面布局提供精确指导
"""

def show_actual_interface_guide():
    """根据用户实际界面显示操作指南"""
    print("🎯 根据您的实际VeighNa界面操作指南")
    print("=" * 60)
    
    print("\n📋 您的界面分析")
    print("-" * 40)
    print("根据您提供的截图，您的VeighNa界面包含：")
    print("")
    print("顶部菜单栏：系统  功能  配置  帮助")
    print("")
    print("左侧面板包含：")
    print("  ├ 交易")
    print("  ├ 交易所 (显示CFFEX)")
    print("  ├ 日志")
    print("  ├ 合约")
    print("  ├ 方向")
    print("  ├ 详情")
    print("  ├ 委托")
    print("  ├ 合约")
    print("  └ 成交")
    print("")
    print("右侧：多个数据表格显示区域")
    
    print("\n🔍 寻找数据管理功能的步骤")
    print("-" * 40)
    print("")
    print("方法1: 通过顶部菜单")
    print("  1️⃣ 点击顶部的 '系统' 菜单")
    print("  2️⃣ 查看下拉菜单中是否有 '数据管理' 选项")
    print("  3️⃣ 如果没有，尝试点击 '功能' 菜单")
    print("  4️⃣ 寻找 '数据管理'、'历史数据'、'数据查询' 等选项")
    print("")
    print("方法2: 通过配置菜单")
    print("  1️⃣ 点击顶部的 '配置' 菜单")
    print("  2️⃣ 查看是否有数据相关的配置选项")
    print("")
    print("方法3: 右键菜单")
    print("  1️⃣ 在主界面空白处右键点击")
    print("  2️⃣ 查看弹出菜单中是否有数据管理选项")
    
    print("\n📊 如果找到数据管理器")
    print("-" * 40)
    print("当您成功打开数据管理器后，应该会看到：")
    print("")
    print("┌─────────────────────────────────────┐")
    print("│  数据管理器                         │")
    print("├─────────────────────────────────────┤")
    print("│  合约代码: [_____________]          │ ← 在这里输入 000001")
    print("│  交易所:   [SZSE ▼]                │ ← 选择 SZSE")
    print("│  开始时间: [2024-01-01]            │")
    print("│  结束时间: [2024-12-31]            │")
    print("│  数据类型: [日线 ▼]                │")
    print("│                                     │")
    print("│  [查询] [导出] [删除]               │")
    print("│                                     │")
    print("│  ┌─────────────────────────────────┐ │")
    print("│  │     查询结果显示区域            │ │")
    print("│  └─────────────────────────────────┘ │")
    print("└─────────────────────────────────────┘")
    
    print("\n🎯 替代方案：使用K线图表")
    print("-" * 40)
    print("如果找不到数据管理器，推荐使用K线图表：")
    print("")
    print("1️⃣ 在顶部菜单中寻找以下选项：")
    print("   - '功能' → 'K线图表'")
    print("   - '功能' → '图表分析'")
    print("   - '系统' → 'K线图表'")
    print("   - 或直接寻找图表相关的菜单项")
    print("")
    print("2️⃣ 在K线图表中：")
    print("   - 合约代码输入: 000001.SZSE")
    print("   - 周期选择: 1d (日线)")
    print("   - 点击查询按钮")
    
    print("\n🔧 如果仍然找不到")
    print("-" * 40)
    print("")
    print("方案A: 检查窗口和面板")
    print("  - 尝试最大化VeighNa窗口")
    print("  - 查看是否有隐藏的面板或标签页")
    print("  - 检查左侧面板是否可以展开更多选项")
    print("")
    print("方案B: 使用快捷键")
    print("  - 尝试按 Ctrl+D (可能是数据管理快捷键)")
    print("  - 尝试按 F2, F3, F4 等功能键")
    print("")
    print("方案C: 重启并检查")
    print("  - 关闭VeighNa")
    print("  - 重新运行: python start_vnpy_enhanced.py")
    print("  - 观察启动时的菜单变化")

def show_data_verification():
    """显示数据验证方法"""
    print("\n✅ 验证数据是否正确导入")
    print("=" * 50)
    
    print("\n📊 数据状态检查:")
    print("-" * 30)
    
    import os
    import sqlite3
    import pandas as pd
    
    # 检查VeighNa数据库
    vnpy_db = "f:/vn/database.db"
    
    if os.path.exists(vnpy_db):
        try:
            conn = sqlite3.connect(vnpy_db)
            
            # 检查表结构
            tables = pd.read_sql("SELECT name FROM sqlite_master WHERE type='table'", conn)
            print(f"✅ VeighNa数据库包含 {len(tables)} 个表")
            
            if 'dbbardata' in tables['name'].values:
                count = pd.read_sql("SELECT COUNT(*) as count FROM dbbardata", conn)['count'].iloc[0]
                print(f"📊 历史数据记录: {count:,} 条")
                
                if count > 0:
                    # 显示可用股票
                    stocks = pd.read_sql(
                        "SELECT DISTINCT symbol, exchange FROM dbbardata ORDER BY symbol LIMIT 10", 
                        conn
                    )
                    print("\n📋 可查询的股票代码:")
                    for _, row in stocks.iterrows():
                        print(f"   ✓ {row['symbol']}.{row['exchange']}")
                    
                    # 显示数据日期范围
                    date_range = pd.read_sql(
                        "SELECT MIN(datetime) as start_date, MAX(datetime) as end_date FROM dbbardata", 
                        conn
                    )
                    print(f"\n📅 数据日期范围: {date_range.iloc[0]['start_date']} 至 {date_range.iloc[0]['end_date']}")
                    
                    print("\n🎯 推荐测试步骤:")
                    print("   1. 在数据管理器中输入: 000001")
                    print("   2. 选择交易所: SZSE")
                    print("   3. 设置日期范围: 2024-01-01 至 2024-12-31")
                    print("   4. 点击查询，应该能看到数据")
                else:
                    print("⚠️  数据库为空，需要重新导入数据")
            else:
                print("❌ 未找到标准数据表，可能需要重新配置")
            
            conn.close()
        except Exception as e:
            print(f"❌ 数据库检查失败: {e}")
    else:
        print("❌ VeighNa数据库文件不存在")

def show_menu_exploration_guide():
    """显示菜单探索指南"""
    print("\n🔍 详细菜单探索指南")
    print("=" * 50)
    
    print("\n📋 逐个检查顶部菜单:")
    print("-" * 30)
    
    print("\n1️⃣ '系统' 菜单可能包含:")
    print("   - 数据管理")
    print("   - 数据服务")
    print("   - 历史数据")
    print("   - 数据导入")
    print("   - 数据查询")
    
    print("\n2️⃣ '功能' 菜单可能包含:")
    print("   - K线图表")
    print("   - 图表分析")
    print("   - 数据分析")
    print("   - 行情显示")
    print("   - 技术分析")
    
    print("\n3️⃣ '配置' 菜单可能包含:")
    print("   - 数据源配置")
    print("   - 接口配置")
    print("   - 系统设置")
    
    print("\n4️⃣ '帮助' 菜单可能包含:")
    print("   - 使用说明")
    print("   - 功能介绍")
    
    print("\n💡 探索技巧:")
    print("-" * 20)
    print("   ✓ 每个菜单都点击一遍，查看所有选项")
    print("   ✓ 注意子菜单和分隔线")
    print("   ✓ 寻找包含'数据'、'图表'、'查询'关键词的选项")
    print("   ✓ 如果菜单项是灰色的，可能需要先连接数据源")

def main():
    """主函数"""
    print("🚀 VeighNa实际界面数据查看指南")
    print("=" * 60)
    print("基于您的实际界面截图提供精确指导")
    print("")
    
    show_actual_interface_guide()
    show_data_verification()
    show_menu_exploration_guide()
    
    print("\n🎉 总结建议")
    print("=" * 30)
    print("")
    print("🎯 优先尝试顺序:")
    print("   1. 系统 → 数据管理")
    print("   2. 功能 → K线图表")
    print("   3. 功能 → 图表分析")
    print("   4. 配置 → 数据源配置")
    print("")
    print("📊 输入格式提醒:")
    print("   - 数据管理器: 代码=000001, 交易所=SZSE")
    print("   - K线图表: 代码=000001.SZSE")
    print("")
    print("🔧 如果都找不到，请运行数据导入脚本确保数据正确加载")
    print("")
    print("✨ 数据已准备就绪，现在去探索您的VeighNa界面吧！")

if __name__ == "__main__":
    main()