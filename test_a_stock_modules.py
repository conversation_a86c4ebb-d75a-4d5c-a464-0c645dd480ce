#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
A股模块测试脚本
测试所有已安装的A股相关组件
"""

import sys
from vnpy.event import EventEngine
from vnpy.trader.engine import MainEngine

def test_a_stock_gateways():
    """测试A股交易接口"""
    print("\n=== 测试A股交易接口 ===")
    
    gateways = {
        "华泰证券TTS": "vnpy_tts.TtsGateway",
        "中泰证券XTP": "vnpy_xtp.XtpGateway",
        "恒生UFT": "vnpy_uft.UftGateway"
    }
    
    for name, module_path in gateways.items():
        try:
            module_name, class_name = module_path.rsplit('.', 1)
            module = __import__(module_name, fromlist=[class_name])
            gateway_class = getattr(module, class_name)
            print(f"✓ {name}: 导入成功")
        except Exception as e:
            print(f"✗ {name}: 导入失败 - {e}")

def test_data_services():
    """测试数据服务"""
    print("\n=== 测试数据服务 ===")
    
    services = {
        "米筐数据RQData": "vnpy_rqdata.Datafeed",
        "TuShare数据": "vnpy_tushare.Datafeed",
        "AkShare数据": "vnpy_akshare.Datafeed"
    }
    
    for name, module_path in services.items():
        try:
            module_name, class_name = module_path.rsplit('.', 1)
            module = __import__(module_name, fromlist=[class_name])
            service_class = getattr(module, class_name)
            print(f"✓ {name}: 导入成功")
        except Exception as e:
            print(f"✗ {name}: 导入失败 - {e}")

def test_apps():
    """测试应用模块"""
    print("\n=== 测试应用模块 ===")
    
    apps = {
        "CTA策略模块": "vnpy_ctastrategy.CtaStrategyApp",
        "CTA回测模块": "vnpy_ctabacktester.CtaBacktesterApp",
        "数据管理模块": "vnpy_datamanager.DataManagerApp",
        "风险管理模块": "vnpy_riskmanager.RiskManagerApp"
    }
    
    for name, module_path in apps.items():
        try:
            module_name, class_name = module_path.rsplit('.', 1)
            module = __import__(module_name, fromlist=[class_name])
            app_class = getattr(module, class_name)
            print(f"✓ {name}: 导入成功")
        except Exception as e:
            print(f"✗ {name}: 导入失败 - {e}")

def test_engine_integration():
    """测试引擎集成"""
    print("\n=== 测试引擎集成 ===")
    
    try:
        # 创建事件引擎和主引擎
        event_engine = EventEngine()
        main_engine = MainEngine(event_engine)
        
        # 测试添加交易接口
        try:
            from vnpy_tts import TtsGateway
            main_engine.add_gateway(TtsGateway)
            print("✓ TTS交易接口: 集成成功")
        except Exception as e:
            print(f"✗ TTS交易接口: 集成失败 - {e}")
        
        try:
            from vnpy_xtp import XtpGateway
            main_engine.add_gateway(XtpGateway)
            print("✓ XTP交易接口: 集成成功")
        except Exception as e:
            print(f"✗ XTP交易接口: 集成失败 - {e}")
        
        try:
            from vnpy_uft import UftGateway
            main_engine.add_gateway(UftGateway)
            print("✓ UFT交易接口: 集成成功")
        except Exception as e:
            print(f"✗ UFT交易接口: 集成失败 - {e}")
        
        # 测试数据服务导入（数据服务通过配置文件配置，不需要手动添加）
        try:
            from vnpy_rqdata import Datafeed as RqdataDatafeed
            print("✓ RQData数据服务: 导入成功")
        except Exception as e:
            print(f"✗ RQData数据服务: 导入失败 - {e}")
        
        try:
            from vnpy_tushare import Datafeed as TushareDatafeed
            print("✓ TuShare数据服务: 导入成功")
        except Exception as e:
            print(f"✗ TuShare数据服务: 导入失败 - {e}")
        
        # 测试添加应用模块
        try:
            from vnpy_ctastrategy import CtaStrategyApp
            main_engine.add_app(CtaStrategyApp)
            print("✓ CTA策略应用: 集成成功")
        except Exception as e:
            print(f"✗ CTA策略应用: 集成失败 - {e}")
        
        try:
            from vnpy_ctabacktester import CtaBacktesterApp
            main_engine.add_app(CtaBacktesterApp)
            print("✓ CTA回测应用: 集成成功")
        except Exception as e:
            print(f"✗ CTA回测应用: 集成失败 - {e}")
        
        try:
            from vnpy_datamanager import DataManagerApp
            main_engine.add_app(DataManagerApp)
            print("✓ 数据管理应用: 集成成功")
        except Exception as e:
            print(f"✗ 数据管理应用: 集成失败 - {e}")
        
        try:
            from vnpy_riskmanager import RiskManagerApp
            main_engine.add_app(RiskManagerApp)
            print("✓ 风险管理应用: 集成成功")
        except Exception as e:
            print(f"✗ 风险管理应用: 集成失败 - {e}")
        
        # 清理资源
        main_engine.close()
        print("\n✓ 引擎集成测试完成")
        
    except Exception as e:
        print(f"✗ 引擎集成测试失败: {e}")

def test_config_files():
    """测试配置文件"""
    print("\n=== 测试配置文件 ===")
    
    import os
    import json
    
    config_files = {
        "A股配置文件": "f:\\vn\\a_stock_config.json",
        "A股启动脚本": "f:\\vn\\start_vnpy_a_stock.py",
        "A股配置指南": "f:\\vn\\A股市场配置指南.md"
    }
    
    for name, filename in config_files.items():
        if os.path.exists(filename):
            print(f"✓ {name}: 文件存在")
            if filename.endswith('.json'):
                try:
                    with open(filename, 'r', encoding='utf-8') as f:
                        json.load(f)
                    print(f"  └─ JSON格式正确")
                except Exception as e:
                    print(f"  └─ JSON格式错误: {e}")
        else:
            print(f"✗ {name}: 文件不存在")

def main():
    """主测试函数"""
    print("VeighNa A股模块测试")
    print("=" * 50)
    print(f"Python版本: {sys.version}")
    
    # 运行所有测试
    test_a_stock_gateways()
    test_data_services()
    test_apps()
    test_engine_integration()
    test_config_files()
    
    print("\n=" * 50)
    print("测试完成！")
    print("\n如果所有项目都显示 ✓，说明A股模块配置成功")
    print("如果有 ✗ 项目，请检查对应模块的安装")
    print("\n下一步：")
    print("1. 运行 python start_vnpy_a_stock.py 启动A股平台")
    print("2. 阅读 A股市场配置指南.md 了解使用方法")
    print("3. 配置交易接口和数据服务")

if __name__ == "__main__":
    main()