3#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
一键下载A股数据工具
最简单的A股数据批量下载方案
"""

import os
import sys
from pathlib import Path

def show_menu():
    """显示菜单"""
    print("🚀 一键下载A股数据工具")
    print("=" * 50)
    print("")
    print("📊 选择下载方案:")
    print("")
    print("1. 🔥 热门股票 (10只热门股票，6个月数据) - 推荐新手")
    print("2. ⚡ 快速测试 (50只股票，3个月数据) - 快速验证")
    print("3. 📈 标准下载 (所有股票，1年数据) - 常用方案")
    print("4. 💾 完整下载 (所有股票，3年数据) - 完整历史")
    print("5. 🛠️  VeighNa集成版 (使用VeighNa数据库)")
    print("6. ❓ 查看帮助")
    print("0. 退出")
    print("")

def download_hot_stocks():
    """下载热门股票数据"""
    print("\n🔥 开始下载热门股票数据...")
    print("这将下载10只热门股票的最近6个月数据")
    print("预计需要1-2分钟")
    
    # 导入并运行热门股票下载
    try:
        from 自动下载全部A股数据 import AStockDataDownloader
        from datetime import datetime, timedelta
        
        # 热门股票列表
        hot_stocks = [
            '000001', '000002', '000858', '000333', '000725',
            '600000', '600036', '600519', '600887', '002415'
        ]
        
        downloader = AStockDataDownloader(
            start_date=(datetime.now() - timedelta(days=180)).strftime('%Y%m%d')
        )
        
        # 创建数据库表
        downloader.create_database_table()
        
        print("\n正在下载热门股票数据...")
        success_count = 0
        total_bars = 0
        
        for i, symbol in enumerate(hot_stocks, 1):
            exchange = 'SSE' if symbol.startswith('6') else 'SZSE'
            print(f"[{i}/10] 下载 {symbol}...")
            
            bars_count, status = downloader.download_stock_data(symbol, f"股票{symbol}", exchange)
            
            if "成功" in status:
                success_count += 1
                total_bars += bars_count
                print(f"✅ {symbol} 成功，获取 {bars_count} 条数据")
            else:
                print(f"❌ {symbol} 失败: {status}")
        
        print(f"\n🎉 热门股票下载完成！")
        print(f"✅ 成功: {success_count}/10 只股票")
        print(f"📊 总数据: {total_bars:,} 条K线")
        
    except Exception as e:
        print(f"❌ 下载失败: {e}")

def run_quick_test():
    """快速测试下载"""
    print("\n⚡ 开始快速测试下载...")
    print("这将下载前50只股票的最近3个月数据")
    print("预计需要3-5分钟")
    
    try:
        from 自动下载全部A股数据 import AStockDataDownloader
        from datetime import datetime, timedelta
        
        downloader = AStockDataDownloader(
            start_date=(datetime.now() - timedelta(days=90)).strftime('%Y%m%d')
        )
        
        downloader.download_all_stocks(max_stocks=50, delay=0.1)
        downloader.generate_report()
        
    except Exception as e:
        print(f"❌ 下载失败: {e}")

def run_standard_download():
    """标准下载"""
    print("\n📈 开始标准下载...")
    print("这将下载所有A股的最近1年数据")
    print("预计需要30-60分钟，请耐心等待")
    
    confirm = input("确认开始下载？(y/N): ").strip().lower()
    if confirm != 'y':
        print("已取消下载")
        return
    
    try:
        from 自动下载全部A股数据 import AStockDataDownloader
        
        downloader = AStockDataDownloader()
        downloader.download_all_stocks(delay=0.1)
        downloader.generate_report()
        
    except Exception as e:
        print(f"❌ 下载失败: {e}")

def run_full_download():
    """完整下载"""
    print("\n💾 开始完整下载...")
    print("这将下载所有A股的最近3年数据")
    print("预计需要1-3小时，建议在空闲时间运行")
    
    confirm = input("确认开始下载？(y/N): ").strip().lower()
    if confirm != 'y':
        print("已取消下载")
        return
    
    try:
        from 自动下载全部A股数据 import AStockDataDownloader
        from datetime import datetime, timedelta
        
        downloader = AStockDataDownloader(
            start_date=(datetime.now() - timedelta(days=1095)).strftime('%Y%m%d')
        )
        
        downloader.download_all_stocks(delay=0.05)
        downloader.generate_report()
        
    except Exception as e:
        print(f"❌ 下载失败: {e}")

def run_vnpy_download():
    """VeighNa集成下载"""
    print("\n🛠️  启动VeighNa集成下载...")
    print("这将使用VeighNa的数据管理功能下载数据")
    
    try:
        os.system("python VeighNa自动下载A股数据.py")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def show_help():
    """显示帮助信息"""
    print("\n❓ 使用帮助")
    print("=" * 40)
    print("")
    print("📋 方案说明:")
    print("")
    print("🔥 热门股票:")
    print("   - 下载10只最热门的A股")
    print("   - 包括平安银行、万科A、贵州茅台等")
    print("   - 适合新手快速体验")
    print("")
    print("⚡ 快速测试:")
    print("   - 下载前50只股票")
    print("   - 用于验证系统是否正常工作")
    print("   - 数据量适中，下载速度快")
    print("")
    print("📈 标准下载:")
    print("   - 下载所有A股（约5000只）")
    print("   - 最近1年的历史数据")
    print("   - 适合日常使用和策略开发")
    print("")
    print("💾 完整下载:")
    print("   - 下载所有A股的3年历史数据")
    print("   - 数据最完整，适合深度分析")
    print("   - 需要较长时间和存储空间")
    print("")
    print("🛠️  VeighNa集成版:")
    print("   - 直接保存到VeighNa数据库")
    print("   - 与VeighNa完美集成")
    print("   - 支持策略回测和实盘交易")
    print("")
    print("💡 使用建议:")
    print("   1. 新手建议先选择'热门股票'模式")
    print("   2. 验证系统正常后再选择'标准下载'")
    print("   3. 下载完成后可在VeighNa中查看数据")
    print("   4. 如遇问题，请检查网络连接")
    print("")

def main():
    """主函数"""
    while True:
        show_menu()
        
        try:
            choice = input("请选择 (0-6): ").strip()
            
            if choice == "0":
                print("\n👋 感谢使用，再见！")
                break
            elif choice == "1":
                download_hot_stocks()
            elif choice == "2":
                run_quick_test()
            elif choice == "3":
                run_standard_download()
            elif choice == "4":
                run_full_download()
            elif choice == "5":
                run_vnpy_download()
            elif choice == "6":
                show_help()
            else:
                print("❌ 无效选择，请重新输入")
            
            if choice in ["1", "2", "3", "4", "5"]:
                input("\n按回车键继续...")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，再见！")
            break
        except Exception as e:
            print(f"\n❌ 程序错误: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()