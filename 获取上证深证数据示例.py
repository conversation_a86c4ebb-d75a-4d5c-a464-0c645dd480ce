#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
获取上证深证数据示例

本脚本演示如何使用AkShare获取上海证券交易所(SSE)和深圳证券交易所(SZSE)的股票数据
"""

import akshare as ak
import pandas as pd
from datetime import datetime, timedelta
import time

def get_stock_list():
    """
    获取A股股票列表
    """
    print("=== 获取A股股票列表 ===")
    
    try:
        # 获取所有A股股票信息
        stock_info = ak.stock_info_a_code_name()
        
        # 分离上证和深证股票
        sse_stocks = stock_info[stock_info['code'].str.startswith(('600', '601', '603', '605'))]
        szse_stocks = stock_info[stock_info['code'].str.startswith(('000', '001', '002', '003'))]
        
        print(f"✓ 上证股票数量: {len(sse_stocks)}")
        print(f"✓ 深证股票数量: {len(szse_stocks)}")
        print(f"✓ 总股票数量: {len(stock_info)}")
        
        return sse_stocks, szse_stocks, stock_info
        
    except Exception as e:
        print(f"✗ 获取股票列表失败: {e}")
        return None, None, None

def get_sample_stocks():
    """
    获取示例股票代码
    """
    return {
        "上证股票": [
            {"code": "600000", "name": "浦发银行", "exchange": "SSE"},
            {"code": "600036", "name": "招商银行", "exchange": "SSE"},
            {"code": "600519", "name": "贵州茅台", "exchange": "SSE"},
            {"code": "601318", "name": "中国平安", "exchange": "SSE"},
            {"code": "601398", "name": "工商银行", "exchange": "SSE"},
        ],
        "深证股票": [
            {"code": "000001", "name": "平安银行", "exchange": "SZSE"},
            {"code": "000002", "name": "万科A", "exchange": "SZSE"},
            {"code": "000858", "name": "五粮液", "exchange": "SZSE"},
            {"code": "000333", "name": "美的集团", "exchange": "SZSE"},
            {"code": "000651", "name": "格力电器", "exchange": "SZSE"},
        ]
    }

def get_realtime_data(stock_code, stock_name):
    """
    获取实时行情数据
    """
    try:
        # 获取实时行情
        realtime_data = ak.stock_zh_a_spot_em()
        
        # 查找指定股票
        stock_data = realtime_data[realtime_data['代码'] == stock_code]
        
        if not stock_data.empty:
            data = stock_data.iloc[0]
            print(f"\n{stock_name} ({stock_code}) 实时行情:")
            print(f"  最新价: {data['最新价']} 元")
            print(f"  涨跌幅: {data['涨跌幅']}%")
            print(f"  涨跌额: {data['涨跌额']} 元")
            print(f"  成交量: {data['成交量']} 手")
            print(f"  成交额: {data['成交额']} 元")
            print(f"  开盘价: {data['今开']} 元")
            print(f"  最高价: {data['最高']} 元")
            print(f"  最低价: {data['最低']} 元")
            print(f"  昨收价: {data['昨收']} 元")
            return True
        else:
            print(f"✗ 未找到股票 {stock_code} 的实时数据")
            return False
            
    except Exception as e:
        print(f"✗ 获取 {stock_code} 实时数据失败: {e}")
        return False

def get_historical_data(stock_code, stock_name, days=30):
    """
    获取历史数据
    """
    try:
        # 计算日期范围
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')
        
        print(f"\n获取 {stock_name} ({stock_code}) 最近{days}天历史数据...")
        
        # 获取历史K线数据
        hist_data = ak.stock_zh_a_hist(symbol=stock_code, period="daily", 
                                      start_date=start_date, end_date=end_date, adjust="")
        
        if not hist_data.empty:
            print(f"✓ 获取到 {len(hist_data)} 条历史数据")
            
            # 显示最近5天数据
            print("\n最近5天数据:")
            recent_data = hist_data.tail(5)
            for _, row in recent_data.iterrows():
                print(f"  {row['日期']}: 开盘{row['开盘']}, 最高{row['最高']}, 最低{row['最低']}, 收盘{row['收盘']}, 成交量{row['成交量']}")
            
            # 计算简单统计信息
            avg_price = hist_data['收盘'].mean()
            max_price = hist_data['最高'].max()
            min_price = hist_data['最低'].min()
            total_volume = hist_data['成交量'].sum()
            
            print(f"\n统计信息 (最近{days}天):")
            print(f"  平均收盘价: {avg_price:.2f} 元")
            print(f"  最高价: {max_price:.2f} 元")
            print(f"  最低价: {min_price:.2f} 元")
            print(f"  总成交量: {total_volume:,} 手")
            
            return hist_data
        else:
            print(f"✗ 未获取到历史数据")
            return None
            
    except Exception as e:
        print(f"✗ 获取历史数据失败: {e}")
        return None

def get_market_overview():
    """
    获取市场概览
    """
    print("\n=== 市场概览 ===")
    
    try:
        # 获取上证指数
        print("\n上证指数:")
        sh_index = ak.stock_zh_index_spot_em(symbol="sh000001")
        if not sh_index.empty:
            data = sh_index.iloc[0]
            print(f"  指数: {data['名称']}")
            print(f"  点位: {data['最新价']}")
            print(f"  涨跌: {data['涨跌额']} ({data['涨跌幅']}%)")
        
        # 获取深证成指
        print("\n深证成指:")
        sz_index = ak.stock_zh_index_spot_em(symbol="sz399001")
        if not sz_index.empty:
            data = sz_index.iloc[0]
            print(f"  指数: {data['名称']}")
            print(f"  点位: {data['最新价']}")
            print(f"  涨跌: {data['涨跌额']} ({data['涨跌幅']}%)")
        
        # 获取创业板指
        print("\n创业板指:")
        cy_index = ak.stock_zh_index_spot_em(symbol="sz399006")
        if not cy_index.empty:
            data = cy_index.iloc[0]
            print(f"  指数: {data['名称']}")
            print(f"  点位: {data['最新价']}")
            print(f"  涨跌: {data['涨跌额']} ({data['涨跌幅']}%)")
            
    except Exception as e:
        print(f"✗ 获取市场概览失败: {e}")

def demo_data_analysis():
    """
    演示数据分析
    """
    print("\n=== 数据分析示例 ===")
    
    # 选择分析股票
    stock_code = "600000"  # 浦发银行
    stock_name = "浦发银行"
    
    # 获取更长期的历史数据
    hist_data = get_historical_data(stock_code, stock_name, days=90)
    
    if hist_data is not None:
        print(f"\n{stock_name} 技术分析:")
        
        # 计算移动平均线
        hist_data['MA5'] = hist_data['收盘'].rolling(window=5).mean()
        hist_data['MA20'] = hist_data['收盘'].rolling(window=20).mean()
        
        # 最新数据
        latest = hist_data.iloc[-1]
        print(f"  最新收盘价: {latest['收盘']:.2f} 元")
        print(f"  5日均线: {latest['MA5']:.2f} 元")
        print(f"  20日均线: {latest['MA20']:.2f} 元")
        
        # 趋势判断
        if latest['收盘'] > latest['MA5'] > latest['MA20']:
            trend = "上升趋势"
        elif latest['收盘'] < latest['MA5'] < latest['MA20']:
            trend = "下降趋势"
        else:
            trend = "震荡趋势"
        
        print(f"  趋势判断: {trend}")
        
        # 计算波动率
        returns = hist_data['收盘'].pct_change().dropna()
        volatility = returns.std() * 100
        print(f"  日波动率: {volatility:.2f}%")

def main():
    """
    主函数
    """
    print("VeighNa AkShare 上证深证数据获取示例")
    print("=" * 50)
    
    # 1. 获取股票列表
    sse_stocks, szse_stocks, all_stocks = get_stock_list()
    
    if all_stocks is None:
        print("无法获取股票列表，程序退出")
        return
    
    # 2. 获取市场概览
    get_market_overview()
    
    # 3. 获取示例股票数据
    sample_stocks = get_sample_stocks()
    
    print("\n=== 获取示例股票实时数据 ===")
    
    # 获取上证股票实时数据
    print("\n--- 上证股票 ---")
    for stock in sample_stocks["上证股票"][:3]:  # 只获取前3只
        get_realtime_data(stock["code"], stock["name"])
        time.sleep(0.5)  # 避免请求过快
    
    # 获取深证股票实时数据
    print("\n--- 深证股票 ---")
    for stock in sample_stocks["深证股票"][:3]:  # 只获取前3只
        get_realtime_data(stock["code"], stock["name"])
        time.sleep(0.5)  # 避免请求过快
    
    # 4. 获取历史数据示例
    print("\n=== 获取历史数据示例 ===")
    get_historical_data("600000", "浦发银行", days=30)
    get_historical_data("000001", "平安银行", days=30)
    
    # 5. 数据分析示例
    demo_data_analysis()
    
    # 6. 使用说明
    print("\n=== 在VeighNa中使用这些数据 ===")
    print("\n1. 配置数据源:")
    print("   - 确保 vt_setting.json 中配置了 datafeed.name = 'akshare'")
    
    print("\n2. 启动VeighNa:")
    print("   python start_vnpy_a_stock.py")
    
    print("\n3. 下载历史数据:")
    print("   - 打开'数据管理'模块")
    print("   - 选择交易所: SSE(上证) 或 SZSE(深证)")
    print("   - 输入股票代码: 如 600000, 000001")
    print("   - 设置时间范围并下载")
    
    print("\n4. 订阅实时行情:")
    print("   - 在交易界面选择对应交易所")
    print("   - 输入股票代码并订阅")
    
    print("\n5. 策略开发:")
    print("   - 使用下载的历史数据进行回测")
    print("   - 使用实时数据进行实盘交易")
    
    print("\n=== 常用股票代码参考 ===")
    for exchange, stocks in sample_stocks.items():
        print(f"\n{exchange}:")
        for stock in stocks:
            print(f"  {stock['code']} - {stock['name']}")
    
    print("\n" + "=" * 50)
    print("✓ 数据获取示例完成！")
    print("现在您可以在VeighNa中使用这些数据进行量化交易了。")

if __name__ == "__main__":
    main()