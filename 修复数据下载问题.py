#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复VeighNa数据下载问题
"""

import akshare as ak
import pandas as pd
from datetime import datetime, timedelta
from vnpy.trader.object import BarData, Exchange, Interval
from vnpy.trader.database import get_database

def test_akshare_connection():
    """测试AkShare连接"""
    print("🔍 测试AkShare连接状态")
    print("=" * 40)
    
    try:
        # 测试获取股票列表
        print("正在获取股票列表...")
        stock_list = ak.stock_info_a_code_name()
        
        if not stock_list.empty:
            print(f"✅ AkShare连接正常，获取到 {len(stock_list)} 只股票")
            
            # 检查600580是否存在
            stock_580 = stock_list[stock_list['code'] == '600580']
            if not stock_580.empty:
                print(f"✅ 600580存在: {stock_580.iloc[0]['name']}")
            else:
                print("❌ 600580不存在于A股列表中")
                
                # 显示一些6005开头的股票
                stocks_6005 = stock_list[stock_list['code'].str.startswith('6005')]
                if not stocks_6005.empty:
                    print("\n📋 6005开头的有效股票:")
                    for _, row in stocks_6005.head(5).iterrows():
                        print(f"   {row['code']} - {row['name']}")
            
            return True
        else:
            print("❌ 无法获取股票列表")
            return False
            
    except Exception as e:
        print(f"❌ AkShare连接失败: {e}")
        return False

def download_stock_data_directly(symbol, exchange="SSE"):
    """直接下载股票数据到VeighNa数据库"""
    print(f"\n📥 直接下载 {symbol}.{exchange} 的数据")
    print("=" * 40)
    
    try:
        # 设置日期范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        
        # 使用AkShare获取数据
        print(f"正在从AkShare获取 {symbol} 的数据...")
        data = ak.stock_zh_a_hist(
            symbol=symbol,
            period='daily',
            start_date=start_date.strftime('%Y%m%d'),
            end_date=end_date.strftime('%Y%m%d')
        )
        
        if data is None or data.empty:
            print(f"❌ 无法获取 {symbol} 的数据")
            return False
        
        print(f"✅ 获取到 {len(data)} 条数据")
        
        # 转换为VeighNa格式
        bars = []
        for _, row in data.iterrows():
            bar = BarData(
                symbol=symbol,
                exchange=Exchange.SSE if exchange == "SSE" else Exchange.SZSE,
                datetime=datetime.strptime(row['日期'], '%Y-%m-%d'),
                interval=Interval.DAILY,
                volume=float(row['成交量']) if pd.notna(row['成交量']) else 0,
                turnover=float(row['成交额']) if pd.notna(row['成交额']) else 0,
                open_interest=0,
                open_price=float(row['开盘']) if pd.notna(row['开盘']) else 0,
                high_price=float(row['最高']) if pd.notna(row['最高']) else 0,
                low_price=float(row['最低']) if pd.notna(row['最低']) else 0,
                close_price=float(row['收盘']) if pd.notna(row['收盘']) else 0,
                gateway_name="akshare"
            )
            bars.append(bar)
        
        # 保存到数据库
        database = get_database()
        database.save_bar_data(bars)
        
        print(f"✅ 成功保存 {len(bars)} 条数据到VeighNa数据库")
        return True
        
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return False

def suggest_valid_stocks():
    """推荐有效的股票代码"""
    print("\n📋 推荐的有效股票代码")
    print("=" * 40)
    
    valid_stocks = [
        ("600000", "SSE", "浦发银行"),
        ("600036", "SSE", "招商银行"),
        ("600519", "SSE", "贵州茅台"),
        ("000001", "SZSE", "平安银行"),
        ("000002", "SZSE", "万科A"),
        ("000858", "SZSE", "五粮液")
    ]
    
    print("🏢 建议使用以下股票代码进行测试:")
    for code, exchange, name in valid_stocks:
        print(f"   {code}.{exchange} - {name}")
    
    return valid_stocks

def main():
    """主函数"""
    print("🔧 VeighNa数据下载问题修复工具")
    print("=" * 50)
    
    # 1. 测试AkShare连接
    connection_ok = test_akshare_connection()
    
    if not connection_ok:
        print("\n❌ AkShare连接失败，请检查网络连接")
        return
    
    # 2. 推荐有效股票
    valid_stocks = suggest_valid_stocks()
    
    # 3. 尝试下载一只有效股票的数据
    print(f"\n🧪 测试下载有效股票数据")
    print("=" * 40)
    
    test_symbol, test_exchange, test_name = valid_stocks[0]  # 600000.SSE
    success = download_stock_data_directly(test_symbol, test_exchange)
    
    if success:
        print(f"\n✅ 数据下载修复成功！")
        print(f"💡 建议在VeighNa中使用 {test_symbol}.{test_exchange} 进行测试")
    else:
        print(f"\n❌ 数据下载仍然失败")
    
    print(f"\n🎯 解决方案总结:")
    print("1. 600580可能不是有效的股票代码")
    print("2. 请使用推荐的有效股票代码")
    print("3. 确保网络连接正常")
    print("4. 在VeighNa中重新尝试下载")

if __name__ == "__main__":
    main()
