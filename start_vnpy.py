#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VeighNa 启动脚本
这是一个简单的VeighNa交易平台启动程序
"""

from vnpy.event import EventEngine
from vnpy.trader.engine import MainEngine
from vnpy.trader.ui import MainWindow, create_qapp

# 可选：导入交易接口（需要单独安装）
# from vnpy_ctp import CtpGateway
# from vnpy_ib import IbGateway

# 可选：导入应用模块（需要单独安装）
# from vnpy_ctastrategy import CtaStrategyApp
# from vnpy_ctabacktester import CtaBacktesterApp
# from vnpy_datamanager import DataManagerApp

def main():
    """启动VeighNa交易平台"""
    print("正在启动VeighNa交易平台...")
    
    # 创建Qt应用程序
    qapp = create_qapp()
    
    # 创建事件引擎
    event_engine = EventEngine()
    
    # 创建主引擎
    main_engine = MainEngine(event_engine)
    
    # 添加交易接口（如果已安装）
    # main_engine.add_gateway(CtpGateway)
    # main_engine.add_gateway(IbGateway)
    
    # 添加应用模块（如果已安装）
    # main_engine.add_app(CtaStrategyApp)
    # main_engine.add_app(CtaBacktesterApp)
    # main_engine.add_app(DataManagerApp)
    
    # 创建主窗口
    main_window = MainWindow(main_engine, event_engine)
    main_window.showMaximized()
    
    print("VeighNa交易平台启动成功！")
    print("提示：如需使用交易接口和应用模块，请先安装相应的扩展包")
    
    # 运行应用程序
    qapp.exec()

if __name__ == "__main__":
    main()