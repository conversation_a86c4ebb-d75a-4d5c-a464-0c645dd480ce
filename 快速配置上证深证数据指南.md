# VeighNa 上证深证数据获取快速指南

## 概述

本指南将帮助您在VeighNa平台中快速配置数据源，获取上海证券交易所(SSE)和深圳证券交易所(SZSE)的股票数据。

## 交易所代码说明

- **SSE**: 上海证券交易所 (Shanghai Stock Exchange)
- **SZSE**: 深圳证券交易所 (Shenzhen Stock Exchange)

## 推荐数据源

### 1. AkShare (免费推荐)

**优势**: 完全免费，数据丰富，支持实时和历史数据

**配置步骤**:

1. **修改配置文件** `vt_setting.json`:
```json
{
    "datafeed.name": "akshare",
    "datafeed.username": "token",
    "datafeed.password": "token"
}
```

2. **启动VeighNa**:
```bash
python start_vnpy_a_stock.py
```

3. **验证配置**:
   - 在VeighNa主界面查看数据源状态
   - 应显示"AkShare数据源已连接"

### 2. TuShare (需要积分)

**配置步骤**:

1. 注册TuShare账号: https://tushare.pro/
2. 获取API Token
3. 修改配置文件:
```json
{
    "datafeed.name": "tushare",
    "datafeed.username": "your_token",
    "datafeed.password": ""
}
```

### 3. RQData (商业版)

**配置步骤**:

1. 注册RiceQuant账号
2. 获取用户名和密码
3. 修改配置文件:
```json
{
    "datafeed.name": "rqdata",
    "datafeed.username": "your_username",
    "datafeed.password": "your_password"
}
```

## 数据获取操作

### 下载历史数据

1. **打开数据管理器**:
   - 在VeighNa主界面点击"功能" → "数据管理"

2. **选择下载参数**:
   - **交易所**: 选择 SSE 或 SZSE
   - **代码**: 输入股票代码(如: 600000, 000001)
   - **开始日期**: 选择起始日期
   - **结束日期**: 选择结束日期
   - **周期**: 选择数据周期(日线、分钟线等)

3. **开始下载**:
   - 点击"下载数据"按钮
   - 等待下载完成

### 订阅实时行情

1. **在交易界面**:
   - 选择交易所: SSE 或 SZSE
   - 输入股票代码: 如 600000
   - 按回车键订阅

2. **查看行情数据**:
   - 实时价格、成交量等信息将显示在行情窗口

## 常用股票代码示例

### 上证股票 (SSE)

| 代码 | 名称 | 完整代码 |
|------|------|----------|
| 600000 | 浦发银行 | 600000.SSE |
| 600036 | 招商银行 | 600036.SSE |
| 600519 | 贵州茅台 | 600519.SSE |
| 601318 | 中国平安 | 601318.SSE |
| 601398 | 工商银行 | 601398.SSE |

### 深证股票 (SZSE)

| 代码 | 名称 | 完整代码 |
|------|------|----------|
| 000001 | 平安银行 | 000001.SZSE |
| 000002 | 万科A | 000002.SZSE |
| 000858 | 五粮液 | 000858.SZSE |
| 000333 | 美的集团 | 000333.SZSE |
| 000651 | 格力电器 | 000651.SZSE |

## 策略中使用数据

### 基本订阅代码

```python
from vnpy_ctastrategy import CtaTemplate

class MyStrategy(CtaTemplate):
    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)
        
        # 订阅上证股票
        self.subscribe("600000.SSE")  # 浦发银行
        
        # 订阅深证股票
        self.subscribe("000001.SZSE")  # 平安银行
    
    def on_tick(self, tick):
        """处理实时行情"""
        if tick.vt_symbol == "600000.SSE":
            print(f"浦发银行最新价: {tick.last_price}")
        elif tick.vt_symbol == "000001.SZSE":
            print(f"平安银行最新价: {tick.last_price}")
```

## 快速测试

运行测试脚本验证配置:

```bash
python 配置AkShare获取上证深证数据.py
```

## 常见问题

### 1. 数据源连接失败

**解决方案**:
- 检查网络连接
- 验证配置文件格式
- 确认API密钥有效性

### 2. 股票代码无数据

**解决方案**:
- 确认股票代码格式正确
- 检查股票是否停牌
- 验证交易所选择正确

### 3. 历史数据下载缓慢

**解决方案**:
- 减少下载时间范围
- 选择较大的数据周期
- 分批下载数据

## 下一步

1. **配置数据源**: 选择适合的数据源并完成配置
2. **下载历史数据**: 获取需要的股票历史数据
3. **测试实时行情**: 验证实时数据订阅功能
4. **开发策略**: 使用获取的数据开发交易策略
5. **回测验证**: 使用历史数据验证策略效果

## 技术支持

- VeighNa官方文档: https://www.vnpy.com/
- AkShare文档: https://akshare.akfamily.xyz/
- 社区论坛: https://github.com/vnpy/vnpy

---

**注意**: 请确保遵守相关交易所的数据使用规定，仅将数据用于合法的投资研究和交易活动。