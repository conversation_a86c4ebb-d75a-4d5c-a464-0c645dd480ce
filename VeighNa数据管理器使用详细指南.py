#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VeighNa数据管理器详细使用指南
帮助用户找到股票代码输入位置和正确使用方法
"""

import os
import sqlite3
import pandas as pd
from datetime import datetime, <PERSON><PERSON><PERSON>

def show_data_manager_guide():
    """显示VeighNa数据管理器详细使用指南"""
    print("🔍 VeighNa数据管理器详细使用指南")
    print("=" * 60)
    
    print("\n📋 步骤1: 打开数据管理器")
    print("-" * 40)
    print("1. 在VeighNa主界面顶部菜单栏中")
    print("2. 点击 '系统' 菜单")
    print("3. 在下拉菜单中选择 '数据管理'")
    print("4. 会弹出一个新的数据管理器窗口")
    
    print("\n🎯 步骤2: 在数据管理器中查找输入框")
    print("-" * 40)
    print("数据管理器窗口通常包含以下区域：")
    print("")
    print("┌─────────────────────────────────────┐")
    print("│  VeighNa数据管理器                    │")
    print("├─────────────────────────────────────┤")
    print("│  合约代码: [_____________]  🔍       │")
    print("│  交易所:   [SZSE ▼]                 │")
    print("│  开始日期: [2024-01-01]             │")
    print("│  结束日期: [2024-12-31]             │")
    print("│  周期:     [1d ▼]                   │")
    print("│                                     │")
    print("│  [查询] [导出] [删除]               │")
    print("│                                     │")
    print("│  ┌─────────────────────────────────┐ │")
    print("│  │     数据显示区域                │ │")
    print("│  │                                 │ │")
    print("│  └─────────────────────────────────┘ │")
    print("└─────────────────────────────────────┘")
    print("")
    
    print("\n📝 步骤3: 正确输入股票代码")
    print("-" * 40)
    print("在 '合约代码' 输入框中输入：")
    print("  ✅ 正确格式: 000001")
    print("  ❌ 错误格式: 000001.SZSE (不要加交易所后缀)")
    print("")
    print("在 '交易所' 下拉框中选择：")
    print("  ✅ 对于000001: 选择 'SZSE'")
    print("  ✅ 对于600000: 选择 'SSE'")
    
    print("\n🎯 步骤4: 设置查询参数")
    print("-" * 40)
    print("1. 开始日期: 建议设置为 2024-01-01")
    print("2. 结束日期: 建议设置为当前日期")
    print("3. 周期: 选择 '1d' (日线数据)")
    print("4. 点击 '查询' 按钮")
    
    print("\n🔧 如果找不到输入框的解决方案")
    print("-" * 40)
    print("方案1: 检查窗口大小")
    print("  - 数据管理器窗口可能太小")
    print("  - 尝试拖拽窗口边缘放大")
    print("  - 或点击窗口最大化按钮")
    print("")
    print("方案2: 检查VeighNa版本")
    print("  - 不同版本界面可能略有差异")
    print("  - 查找类似的输入控件")
    print("")
    print("方案3: 使用K线图表替代")
    print("  - 点击 '应用' -> 'K线图表'")
    print("  - 在K线图表中输入: 000001.SZSE")
    print("  - 这个方法更直观")

def show_alternative_methods():
    """显示查看数据的替代方法"""
    print("\n🔄 替代方法: 使用K线图表查看数据")
    print("=" * 50)
    
    print("\n📊 K线图表使用步骤:")
    print("-" * 30)
    print("1. 在VeighNa主界面点击 '应用' 菜单")
    print("2. 选择 'K线图表'")
    print("3. 在弹出的K线图表窗口中:")
    print("   - 合约代码输入: 000001.SZSE")
    print("   - 选择周期: 1d (日线)")
    print("   - 点击 '查询' 按钮")
    print("4. 如果有数据，会显示K线图")
    
    print("\n💡 K线图表的优势:")
    print("-" * 30)
    print("✅ 界面更直观")
    print("✅ 输入框更明显")
    print("✅ 可以直观看到价格走势")
    print("✅ 支持多种技术指标")

def check_data_availability():
    """检查数据可用性"""
    print("\n🔍 检查数据可用性")
    print("=" * 40)
    
    # 检查VeighNa数据库
    vnpy_db = "f:/vn/database.db"
    optimized_db = "f:/vn/optimized_stock_data.db"
    
    print("\n📊 数据库状态检查:")
    print("-" * 30)
    
    if os.path.exists(vnpy_db):
        try:
            conn = sqlite3.connect(vnpy_db)
            tables = pd.read_sql("SELECT name FROM sqlite_master WHERE type='table'", conn)
            
            if 'dbbardata' in tables['name'].values:
                count = pd.read_sql("SELECT COUNT(*) as count FROM dbbardata", conn)['count'].iloc[0]
                print(f"✅ VeighNa数据库: {count:,} 条记录")
                
                if count > 0:
                    # 显示可用股票
                    stocks = pd.read_sql(
                        "SELECT DISTINCT symbol, exchange FROM dbbardata LIMIT 10", 
                        conn
                    )
                    print("\n📋 可查询的股票代码示例:")
                    for _, row in stocks.iterrows():
                        print(f"   - 代码: {row['symbol']}, 交易所: {row['exchange']}")
                else:
                    print("⚠️  VeighNa数据库为空")
            else:
                print("❌ VeighNa数据库无数据表")
            
            conn.close()
        except Exception as e:
            print(f"❌ VeighNa数据库检查失败: {e}")
    else:
        print("❌ VeighNa数据库不存在")
    
    # 检查下载的数据
    if os.path.exists(optimized_db):
        try:
            conn = sqlite3.connect(optimized_db)
            count = pd.read_sql("SELECT COUNT(*) as count FROM stock_daily_data", conn)['count'].iloc[0]
            print(f"✅ 已下载数据: {count:,} 条记录")
            
            if count > 0:
                print("\n💡 数据导入建议:")
                print("   运行: python 诊断VeighNa数据显示问题.py")
                print("   这将把下载的数据导入到VeighNa数据库")
            
            conn.close()
        except Exception as e:
            print(f"❌ 下载数据检查失败: {e}")
    else:
        print("❌ 下载数据不存在")

def show_troubleshooting():
    """显示故障排除指南"""
    print("\n🛠️  故障排除指南")
    print("=" * 40)
    
    print("\n❓ 问题1: 找不到输入框")
    print("-" * 30)
    print("解决方案:")
    print("1. 确保数据管理器窗口完全打开")
    print("2. 尝试最大化窗口")
    print("3. 检查是否有滚动条，向上滚动")
    print("4. 重启VeighNa重新打开")
    
    print("\n❓ 问题2: 输入代码后无数据")
    print("-" * 30)
    print("解决方案:")
    print("1. 确认股票代码格式正确 (如: 000001)")
    print("2. 确认交易所选择正确 (如: SZSE)")
    print("3. 检查日期范围设置")
    print("4. 运行数据导入脚本")
    
    print("\n❓ 问题3: 数据管理器打不开")
    print("-" * 30)
    print("解决方案:")
    print("1. 重启VeighNa")
    print("2. 检查是否有错误日志")
    print("3. 尝试使用K线图表替代")
    
    print("\n🎯 推荐操作流程")
    print("-" * 30)
    print("1. 先尝试K线图表方法 (更简单)")
    print("2. 如果K线图表有数据，说明数据正常")
    print("3. 再回到数据管理器尝试查询")
    print("4. 如果都没有数据，运行数据导入脚本")

def main():
    """主函数"""
    print("🚀 VeighNa数据查看完整指南")
    print("=" * 60)
    print("解决'找不到输入代码的地方'的问题")
    print("")
    
    show_data_manager_guide()
    show_alternative_methods()
    check_data_availability()
    show_troubleshooting()
    
    print("\n✨ 总结")
    print("=" * 30)
    print("1. 🎯 推荐使用K线图表 (应用 -> K线图表)")
    print("2. 📊 输入格式: 000001.SZSE")
    print("3. 🔧 如无数据: 运行诊断脚本")
    print("4. 💡 数据管理器: 系统 -> 数据管理")
    print("")
    print("🎉 现在您应该能够成功查看股票数据了！")

if __name__ == "__main__":
    main()