#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复VeighNa数据下载日期问题
解决未来日期导致的数据下载失败问题
"""

import akshare as ak
from datetime import datetime, timedelta
import pandas as pd

def check_date_issue():
    """检查日期设置问题"""
    print("=== 日期问题诊断 ===")
    
    # 获取当前日期
    today = datetime.now()
    print(f"今天日期: {today.strftime('%Y/%m/%d')}")
    
    # 检查用户设置的日期
    user_date = "2025/8/20"
    print(f"您设置的开始日期: {user_date}")
    
    try:
        user_datetime = datetime.strptime(user_date, "%Y/%m/%d")
        if user_datetime > today:
            print("❌ 问题发现: 您设置的是未来日期！")
            print("📅 股票数据只能获取历史数据，不能获取未来数据")
            return False
        else:
            print("✅ 日期设置正确")
            return True
    except Exception as e:
        print(f"❌ 日期格式错误: {e}")
        return False

def get_recommended_dates():
    """获取推荐的日期设置"""
    print("\n=== 推荐日期设置 ===")
    
    today = datetime.now()
    
    # 推荐最近30天
    start_date_30d = today - timedelta(days=30)
    print(f"最近30天数据: {start_date_30d.strftime('%Y/%m/%d')} 到 {today.strftime('%Y/%m/%d')}")
    
    # 推荐最近90天
    start_date_90d = today - timedelta(days=90)
    print(f"最近90天数据: {start_date_90d.strftime('%Y/%m/%d')} 到 {today.strftime('%Y/%m/%d')}")
    
    # 推荐今年数据
    start_date_year = datetime(today.year, 1, 1)
    print(f"今年数据: {start_date_year.strftime('%Y/%m/%d')} 到 {today.strftime('%Y/%m/%d')}")
    
    return {
        '30天': start_date_30d.strftime('%Y/%m/%d'),
        '90天': start_date_90d.strftime('%Y/%m/%d'),
        '今年': start_date_year.strftime('%Y/%m/%d'),
        '结束': today.strftime('%Y/%m/%d')
    }

def test_data_download_with_correct_date():
    """使用正确日期测试数据下载"""
    print("\n=== 测试数据下载 ===")
    
    # 获取推荐日期
    dates = get_recommended_dates()
    
    # 测试股票代码
    test_codes = ['000001', '000002', '600000']
    
    for code in test_codes:
        print(f"\n测试股票: {code}")
        try:
            # 获取最近30天的数据
            start_date = dates['30天'].replace('/', '')
            end_date = dates['结束'].replace('/', '')
            
            print(f"获取日期范围: {start_date} 到 {end_date}")
            
            # 获取股票历史数据
            df = ak.stock_zh_a_hist(symbol=code, period="daily", 
                                  start_date=start_date, end_date=end_date)
            
            if df is not None and len(df) > 0:
                print(f"✅ 成功获取 {len(df)} 条数据")
                print(f"数据日期范围: {df['日期'].min()} 到 {df['日期'].max()}")
                print(f"最新收盘价: {df['收盘'].iloc[-1]}")
            else:
                print("❌ 未获取到数据")
                
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")

def show_correct_vnpy_settings():
    """显示VeighNa中的正确设置"""
    print("\n=== VeighNa正确设置指南 ===")
    
    dates = get_recommended_dates()
    
    print("📋 在VeighNa数据下载界面中，请这样设置:")
    print("")
    print("代码: 000001")
    print("交易所: SZSE")
    print("周期: DAILY")
    print(f"开始日期: {dates['30天']} (推荐最近30天)")
    print("")
    print("⚠️  重要提醒:")
    print("1. 开始日期必须是过去的日期，不能是未来日期")
    print("2. 建议先测试较短时间范围（如最近30天）")
    print("3. 确认数据下载成功后，再尝试更长时间范围")
    print("4. 避免选择周末和节假日作为开始日期")

def main():
    """主函数"""
    print("VeighNa数据下载问题修复工具")
    print("=" * 50)
    
    # 检查日期问题
    is_date_ok = check_date_issue()
    
    # 获取推荐日期
    get_recommended_dates()
    
    # 测试数据下载
    test_data_download_with_correct_date()
    
    # 显示正确设置
    show_correct_vnpy_settings()
    
    print("\n=== 总结 ===")
    if not is_date_ok:
        print("🔍 问题原因: 您设置的开始日期是未来日期 (2025/8/20)")
        print("💡 解决方案: 将开始日期改为过去的日期")
        print("📅 推荐设置: 使用上面显示的推荐日期")
    else:
        print("✅ 日期设置正确，可能是其他问题")
    
    print("\n🚀 下一步操作:")
    print("1. 关闭当前的VeighNa数据下载对话框")
    print("2. 重新打开数据下载功能")
    print("3. 使用上面推荐的日期设置")
    print("4. 点击下载按钮")

if __name__ == "__main__":
    main()