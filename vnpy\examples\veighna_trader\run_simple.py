from vnpy.event import EventEngine

from vnpy.trader.engine import MainEngine
from vnpy.trader.ui import MainWindow, create_qapp

from vnpy_ctp import CtpGateway
# A股相关接口
from vnpy_xtp import XtpGateway
from vnpy_tora import ToraStockGateway
# from vnpy_ost import OstGateway
# from vnpy_emt import EmtGateway

from vnpy_ctastrategy import CtaStrategyApp
from vnpy_ctabacktester import CtaBacktesterApp
# 添加数据管理器
from vnpy_datamanager import DataManagerApp
# 添加数据记录器
from vnpy_datarecorder import DataRecorderApp


def main():
    """"""
    qapp = create_qapp()

    event_engine = EventEngine()

    main_engine = MainEngine(event_engine)

    # 添加期货接口
    main_engine.add_gateway(CtpGateway)
    # 添加A股接口
    main_engine.add_gateway(XtpGateway)
    main_engine.add_gateway(ToraStockGateway)
    # main_engine.add_gateway(OstGateway)
    # main_engine.add_gateway(EmtGateway)

    main_engine.add_app(CtaStrategyApp)
    main_engine.add_app(CtaBacktesterApp)
    # 添加数据管理应用
    main_engine.add_app(DataManagerApp)
    # 添加数据记录应用
    main_engine.add_app(DataRecorderApp)

    main_window = MainWindow(main_engine, event_engine)
    main_window.showMaximized()

    qapp.exec()


if __name__ == "__main__":
    main()