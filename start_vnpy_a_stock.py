#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VeighNa A股市场专用启动脚本
包含中国A股市场相关的交易接口、数据服务和应用模块
"""

from vnpy.event import EventEngine
from vnpy.trader.engine import MainEngine
from vnpy.trader.ui import MainWindow, create_qapp

# A股交易接口
from vnpy_tts import TtsGateway          # 华泰证券
from vnpy_xtp import XtpGateway          # 中泰证券
from vnpy_uft import UftGateway          # 恒生UFT

# A股数据服务
from vnpy_rqdata import Datafeed as RqdataDatafeed    # 米筐数据
from vnpy_tushare import Datafeed as TushareDatafeed   # TuShare数据
from vnpy_akshare import Datafeed as AkshareDatafeed   # AkShare数据

# 应用模块
from vnpy_ctastrategy import CtaStrategyApp      # CTA策略
from vnpy_ctabacktester import CtaBacktesterApp  # CTA回测
# from vnpy_datamanager import DataManagerApp      # 数据管理 - 暂时注释掉
from vnpy_riskmanager import RiskManagerApp      # 风险管理

# 尝试导入数据管理器，如果失败则跳过
try:
    from vnpy_datamanager import DataManagerApp
    DATA_MANAGER_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  数据管理器模块导入失败: {e}")
    print("💡 将跳过数据管理器模块，其他功能正常使用")
    DATA_MANAGER_AVAILABLE = False

def main():
    """启动VeighNa A股交易平台"""
    print("正在启动VeighNa A股交易平台...")
    
    # 创建Qt应用程序
    qapp = create_qapp()
    
    # 创建事件引擎
    event_engine = EventEngine()
    
    # 创建主引擎
    main_engine = MainEngine(event_engine)
    
    # 添加A股交易接口
    print("正在加载A股交易接口...")
    main_engine.add_gateway(TtsGateway)  # 华泰证券
    main_engine.add_gateway(XtpGateway)  # 中泰证券
    main_engine.add_gateway(UftGateway)  # 恒生UFT
    print("A股交易接口加载完成")
    
    # 数据服务通过配置文件配置，不需要手动添加
    # 可以通过全局配置文件设置datafeed.name等参数来使用数据服务
    print("数据服务配置完成")
    
    # 添加应用模块
    print("正在加载应用模块...")
    main_engine.add_app(CtaStrategyApp)      # CTA策略
    main_engine.add_app(CtaBacktesterApp)    # CTA回测
    if DATA_MANAGER_AVAILABLE:
        main_engine.add_app(DataManagerApp)      # 数据管理
        print("✅ 数据管理模块加载成功")
    else:
        print("⚠️  跳过数据管理模块")
    main_engine.add_app(RiskManagerApp)      # 风险管理
    print("应用模块加载完成")
    
    # 创建主窗口
    main_window = MainWindow(main_engine, event_engine)
    main_window.showMaximized()
    
    print("="*50)
    print("VeighNa A股交易平台启动成功！")
    print("="*50)
    print("已加载的A股交易接口：")
    print("  - 华泰证券 TTS")
    print("  - 中泰证券 XTP")
    print("  - 恒生 UFT")
    print("\n已加载的数据服务：")
    print("  - 米筐数据 RQData")
    print("  - TuShare数据")
    print("\n已加载的应用模块：")
    print("  - CTA策略模块")
    print("  - CTA回测模块")
    if DATA_MANAGER_AVAILABLE:
        print("  - 数据管理模块")
    else:
        print("  - 数据管理模块 (未加载)")
    print("  - 风险管理模块")
    print("="*50)
    print("使用说明：")
    print("1. 在'系统'菜单中选择'连接'配置交易账户")
    print("2. 使用'数据管理'模块下载历史数据")
    print("3. 在'CTA策略'模块中开发和运行策略")
    print("4. 使用'CTA回测'模块测试策略效果")
    print("5. 通过'风险管理'模块控制交易风险")
    print("="*50)
    
    # 运行应用程序
    qapp.exec()

if __name__ == "__main__":
    main()