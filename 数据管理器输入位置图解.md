# VeighNa数据管理器输入位置详细图解

## 🎯 问题解决：在哪里输入股票代码？

### 📍 第一步：打开数据管理器

根据您的实际界面，VeighNa主界面是这样的：

1. **在VeighNa主界面顶部菜单栏**
2. **点击 `系统` 菜单**
3. **选择 `数据管理`**

```
VeighNa实际主界面
┌─────────────────────────────────────────────────┐
│ 系统  功能  配置  帮助                           │ ← 实际菜单栏
├─────────────────────────────────────────────────┤
│ 左侧面板：                                      │
│ ├ 交易                                          │
│ ├ 交易所 CFFEX                                  │
│ ├ 日志                                          │
│ ├ 合约                                          │
│ ├ 方向                                          │
│ ├ 详情                                          │
│ ├ 委托                                          │
│ ├ 合约                                          │
│ └ 成交                                          │
│                                                 │
│ 右侧：多个数据表格区域                           │
└─────────────────────────────────────────────────┘
```

**重要提示：** 根据您的界面，菜单可能是 `系统` 或 `功能`，请在顶部菜单栏中寻找包含"数据管理"选项的菜单。

### 📍 第二步：数据管理器窗口布局

数据管理器打开后，您会看到这样的界面：

```
数据管理器窗口
┌─────────────────────────────────────────────────┐
│  VeighNa数据管理器                                │
├─────────────────────────────────────────────────┤
│                                                 │
│  合约代码: [________________]  🔍 查询           │ ← 这里输入股票代码
│                                                 │
│  交易所:   [SZSE            ▼]                 │ ← 选择交易所
│                                                 │
│  开始日期: [2024-01-01      ]                   │
│  结束日期: [2024-12-31      ]                   │
│  周期:     [1d              ▼]                 │
│                                                 │
│  [查询数据] [导出数据] [删除数据]                │
│                                                 │
│  ┌─────────────────────────────────────────────┐ │
│  │           数据显示区域                      │ │
│  │                                             │ │
│  │  (这里会显示查询到的股票数据)                │ │
│  │                                             │ │
│  └─────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────┘
```

### 🎯 第三步：正确输入股票代码

**在"合约代码"输入框中输入：**

✅ **正确格式：**
- 输入：`000001`
- 交易所选择：`SZSE`

❌ **错误格式：**
- ~~000001.SZSE~~ (不要在代码框中加交易所后缀)
- ~~sz000001~~ (不要加前缀)

### 📋 常用股票代码示例

| 股票名称 | 代码输入 | 交易所选择 |
|---------|---------|----------|
| 平安银行 | `000001` | `SZSE` |
| 万科A   | `000002` | `SZSE` |
| 浦发银行 | `600000` | `SSE` |
| 中国银行 | `601988` | `SSE` |

### 🔧 如果找不到输入框怎么办？

#### 方案1：调整窗口大小
```
如果窗口太小，输入框可能被隐藏
┌─────────────────┐
│ 数据管理器      │ ← 窗口太小
├─────────────────┤
│ [看不到输入框]   │
└─────────────────┘

拖拽窗口边缘放大 ↓

┌─────────────────────────────────┐
│ 数据管理器                      │ ← 窗口放大后
├─────────────────────────────────┤
│ 合约代码: [_______] 🔍          │ ← 现在能看到了
│ 交易所:   [SZSE ▼]             │
└─────────────────────────────────┘
```

#### 方案2：使用K线图表（推荐）

如果数据管理器有问题，可以使用K线图表：

1. **在顶部菜单栏寻找 `功能` 或 `应用` 菜单**
2. **选择 `K线图表` 或 `图表分析`**
3. **在K线图表中输入：`000001.SZSE`**

**注意：** 根据您的界面版本，菜单名称可能略有不同，请寻找与图表、分析相关的菜单项。

```
K线图表界面
┌─────────────────────────────────────────────────┐
│  K线图表                                        │
├─────────────────────────────────────────────────┤
│                                                 │
│  合约代码: [000001.SZSE        ]  🔍 查询       │ ← 这里输入完整代码
│                                                 │
│  周期: [1d ▼]  [查询] [重置]                    │
│                                                 │
│  ┌─────────────────────────────────────────────┐ │
│  │                                             │ │
│  │           K线图显示区域                     │ │
│  │                                             │ │
│  └─────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────┘
```

### ✅ 完整操作流程

1. **打开VeighNa** → 运行 `python start_vnpy_enhanced.py`
2. **进入数据管理器** → 在顶部菜单栏找到 `系统` 或 `功能` → 数据管理
3. **输入股票代码** → 在"合约代码"框输入 `000001`
4. **选择交易所** → 选择 `SZSE`
5. **设置日期范围** → 开始日期：2024-01-01，结束日期：当前日期
6. **点击查询** → 点击"查询数据"按钮
7. **查看结果** → 在下方数据显示区域查看股票数据

### 🔍 界面差异说明

不同版本的VeighNa界面可能有所不同：
- 有些版本菜单是：`文件` `系统` `应用` `算法` `帮助`
- 有些版本菜单是：`系统` `功能` `配置` `帮助`
- 数据管理功能通常在 `系统` 或 `功能` 菜单下
- 如果找不到，请查看所有顶部菜单项

### 🎉 现在您应该能够成功找到输入位置并查看股票数据了！

---

**💡 小贴士：**
- 如果数据管理器界面与上述描述不完全一致，这是正常的，不同版本可能略有差异
- 重点是找到"合约代码"或"股票代码"输入框
- 推荐优先使用K线图表，界面更直观
- 数据已经成功导入，重启VeighNa后即可查看