# 算法交易执行

Veighna Elite Trader提供了算法委托执行交易支持。


## 功能简介

用户可以通过Veighna Elite Trader主界面的【算法交易】组件来便捷完成启动算法、停止算法等任务。

【算法交易】组件采用独立进程的算法引擎负责委托订单的具体执行过程。目前提供了五种示例算法，用户可以通过把大笔订单自动拆分成合适的小单分批委托的操作，有效降低交易成本和冲击成本，也可以在设定的阈值内进行高抛低吸操作。


## 启动执行

启动Veighna Elite Trader并连接接口之后，点击主界面左侧中部的【算法交易】标签页，就可以看见算法执行组件的操作界面，如下图所示：

![](https://vnpy-doc.oss-cn-shanghai.aliyuncs.com/elite/algo/1.png)

在单元格【算法】对应的下拉框中选择要启动的算法，如下图所示：

![](https://vnpy-doc.oss-cn-shanghai.aliyuncs.com/elite/algo/2.png)

选择好算法之后，对算法的参数进行配置，点击【启动算法】即可进行算法委托。此时Veighna Elite Trader主界面右侧中上部分的【算法】标签页会显示该算法的执行情况，右侧中上部分的【委托】标签页以及右侧中下部分的【成交】标签页则会显示相关的委托成交情况，如下图所示：

![](https://vnpy-doc.oss-cn-shanghai.aliyuncs.com/elite/algo/5.png)

![](https://vnpy-doc.oss-cn-shanghai.aliyuncs.com/elite/algo/6.png)

若需停止所有启动的算法，点击【全部停止】按钮即可停止所有算法。


## 算法状态监控

启动Veighna Elite Trader之后，点击主界面右侧中上部分的【算法】标签页，就可以看见算法状态监控组件。

算法状态监控组件用于记录已启动算法的名称、本地代码、方向、开平、价格、总数量、成交量、剩余量、成交均价、状态、参数以及变量。除此之外，组件每行最左侧还嵌入了两个控制单个算法状态的按钮，如下图所示：

![](https://vnpy-doc.oss-cn-shanghai.aliyuncs.com/elite/algo/7.png)

### 算法状态

一个算法具有运行、暂停、停止和结束四种状态。当算法启动时，算法处于“运行”状态（RUNNING）。当算法因为完成任务主动结束（计时到了或成交量满足条件等）时，算法处于“结束”状态（FINISHED）。当算法被暂停，算法处于“暂停”状态（PAUSED）。当算法被动结束，算法处于“停止”状态（STOPPED）。

### 算法控制按钮

当算法启动后，【算法】组件上会显示算法的状态信息。此时该算法信息最左侧的【停止】和【暂停】按钮皆处于可点击状态。若该算法主动结束了，此时该算法最左侧的两个按钮区域皆会变灰（不可点击）。

#### 暂停算法

若想暂停算法的执行，可点击【暂停】按钮，此时原来【暂停】按钮上的文字会变为“恢复”。

#### 恢复算法

如果想恢复已暂停算法的执行，则可点击【恢复】按钮进行恢复，此时原来【恢复】按钮上的文字会变为“暂停”。

#### 停止算法

若想停止该运行中的算法，可以点击【停止】按钮停止算法，此时该算法最左侧的两个按钮区域皆会变灰（不可点击）。


## 算法介绍

目前，【算法交易】组件提供了以下五种内置算法：

### TWAP - 时间加权平均算法

时间加权平均算法（TWAP）具体执行步骤如下：

- 将委托数量平均分布在某个时间区域内，每隔一段时间用指定的价格挂出买单（或者卖单）。

- 买入情况：卖一价低于目标价格时，发出委托，委托数量在剩余委托量与委托分割量中取最小值。

- 卖出情况：买一价高于目标价格时，发出委托，委托数量在剩余委托量与委托分割量中取最小值。

### Iceberg - 冰山算法

冰山算法（Iceberg）具体执行步骤如下：

- 在某个价位挂单，但是只挂一部分，直到全部成交。

- 买入情况：先检查撤单，最新Tick卖一价低于目标价格，执行撤单；若无活动委托，发出委托，委托数量在剩余委托量与挂出委托量中取最小值。

- 卖出情况：先检查撤单，最新Tick买一价高于目标价格，执行撤单；若无活动委托，发出委托，委托数量在剩余委托量与挂出委托量中取最小值。

### Sniper - 狙击手算法

狙击手算法（Sniper）具体执行步骤如下：

- 监控最新Tick推送的行情，发现好的价格立刻报价成交。

- 买入情况：最新Tick卖一价低于目标价格时，发出委托，委托数量在剩余委托量与卖一量中取最小值。

- 卖出情况：最新Tick买一价高于目标价格时，发出委托，委托数量在剩余委托量与买一量中取最小值。

### Stop - 条件委托算法

条件委托算法（Stop）具体执行步骤如下：

- 监控最新Tick推送的行情，发现行情突破立刻报价成交。

- 买入情况：Tick最新价高于目标价格时，发出委托，委托价为目标价格加上超价。

- 卖出情况：Tick最新价低于目标价格时，发出委托，委托价为目标价格减去超价。

### BestLimit - 最优限价算法

最优限价算法（BestLimit）具体执行步骤如下：

- 监控最新Tick推送的行情，发现好的价格立刻报价成交。

- 买入情况：先检查撤单：最新Tick买一价不等于目标价格时，执行撤单；若无活动委托，发出委托，委托价格为最新Tick买一价，委托数量为剩余委托量。

- 卖出情况：先检查撤单：最新Tick买一价不等于目标价格时，执行撤单；若无活动委托，发出委托，委托价格为最新Tick卖一价，委托数量为剩余委托量。


## 多账户支持

### 加载

算法委托执行交易模块提供了多账户批量下单交易支持（手动）。

以登录**CTP**接口为例，在登录界面下方的【交易接口】标签页的下拉框中先选中CTP接口。在“自定义接口”处填写自定义的接口名（例如“CTP1”、“CTP2”）之后点击【添加】按钮，填写子账户的配置信息，点击【确定】按钮，则可依次加载对应账户的接口。

添加完毕后，点击登录界面的【登录】按钮登录VeighNa Elite Trader。在菜单栏中依次点击【系统】->【连接xxx】（xxx是自定义的接口名，若加载时填写的“CTP1”，则菜单栏中显示的就是【连接CTP1】），即可连接子账户接口。

连接成功以后，VeighNa Elite Trader主界面【日志】组件会立刻输出登录相关信息，同时用户也可以看到对应的账号信息，持仓信息等相关信息。

此时已经可以在【市场深度交易】界面通过指定的账户进行委托了。

### 算法下单

#### 账户下单

若需通过【算法交易】组件进行多账户委托，可在创建的图表左上角选择账户对应的【接口】启动算法。

发出委托后，可以在VeighNa Elite Trader主界面【算法】、【委托】和【成交】组件上跟踪到根据对应接口下单的委托，如下图所示：

![](https://vnpy-doc.oss-cn-shanghai.aliyuncs.com/elite/algo/4.png)

**请注意**：
 - 目前支持同时登录最多登录5个交易账户
