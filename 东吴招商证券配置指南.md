# 东吴证券和招商证券VeighNa配置指南

## 🎯 概述

虽然VeighNa没有专门的东吴证券和招商证券接口，但您仍然可以通过以下方式使用VeighNa进行A股交易：

## 🔌 可用的交易接口选项

### 方案1：恒生UFT接口（推荐）
**适用券商**：支持恒生柜台的券商（包括部分东吴、招商营业部）

**配置步骤**：
1. 联系您的券商确认是否支持恒生UFT接口
2. 获取UFT接口的连接参数：
   - 交易服务器地址
   - 行情服务器地址
   - 用户名和密码
   - 授权码等
3. 在VeighNa中选择"恒生UFT"接口进行连接

### 方案2：中泰XTP接口
**适用场景**：如果您的券商支持XTP协议

**特点**：
- 支持A股现货交易
- 支持融资融券
- 支持ETF期权交易

### 方案3：华泰TTS接口
**适用场景**：部分券商可能支持TTS协议

## 📊 数据源配置

### AkShare数据源（免费）
**优势**：
- ✅ 完全免费
- ✅ 数据丰富
- ✅ 支持A股全市场
- ✅ 实时更新

**配置状态**：
- 已在 `vt_setting.json` 中配置为默认数据源
- 重启VeighNa后即可使用

### 其他数据源选项
- **TuShare**：需要积分或付费
- **米筐RQData**：专业数据服务

## 🚀 启动VeighNa

### 使用增强版启动脚本
```bash
python start_vnpy_enhanced.py
```

### 使用原版启动脚本
```bash
python start_vnpy_a_stock.py
```

## 🔧 连接券商的具体步骤

### 步骤1：确认券商支持的接口类型
**联系方式**：
- 致电券商客服：95533（招商证券）、400-8888-111（东吴证券）
- 询问是否支持：恒生UFT、中泰XTP、华泰TTS等接口

### 步骤2：获取接口参数
**需要获取的信息**：
- 交易服务器地址和端口
- 行情服务器地址和端口
- 用户名（通常是资金账号）
- 密码
- 授权码或产品号（如需要）

### 步骤3：在VeighNa中配置
1. 启动VeighNa
2. 点击"系统" -> "连接管理"
3. 选择对应的接口类型
4. 填入获取的连接参数
5. 点击"连接"

## 📈 数据查看指南

### 查看已下载的96只股票数据
1. **数据管理器**：
   - 系统 -> 数据管理
   - 输入股票代码：`000001`
   - 选择交易所：`SZSE`
   - 点击查询

2. **K线图表**：
   - 应用 -> K线图表
   - 输入：`000001.SZSE`
   - 选择周期：日线
   - 点击查询

### 股票代码格式
- **深交所**：`000001.SZSE`（平安银行）
- **上交所**：`600000.SSE`（浦发银行）
- **创业板**：`300001.SZSE`
- **科创板**：`688001.SSE`

## 🛠️ 故障排除

### 问题1：看不到AkShare数据源
**解决方案**：
```bash
# 重启VeighNa
python start_vnpy_enhanced.py
```

### 问题2：无法连接券商接口
**检查清单**：
- ✅ 确认券商支持该接口类型
- ✅ 验证连接参数正确性
- ✅ 检查网络连接
- ✅ 确认交易时间段

### 问题3：数据显示为空
**解决方案**：
```bash
# 运行数据诊断工具
python 诊断VeighNa数据显示问题.py
```

## 💡 重要提示

### 关于交易接口
- VeighNa主要支持标准化的交易协议
- 不同券商可能使用不同的底层技术
- 建议优先尝试恒生UFT接口（覆盖面最广）

### 关于数据源
- AkShare数据源完全免费且功能强大
- 适合个人投资者和小型机构使用
- 数据质量和及时性良好

### 下一步建议
1. **立即行动**：联系券商确认支持的接口类型
2. **测试连接**：使用模拟环境测试接口连接
3. **数据验证**：确认历史数据和实时数据正常
4. **策略开发**：开始开发和测试交易策略

## 📞 技术支持

如果遇到技术问题，可以：
1. 查看VeighNa官方文档
2. 参考本目录下的其他指南文档
3. 运行诊断脚本进行自动检测和修复

---

**记住**：即使没有专门的东吴/招商接口，通过标准化的交易协议，您仍然可以充分利用VeighNa的强大功能进行量化交易！