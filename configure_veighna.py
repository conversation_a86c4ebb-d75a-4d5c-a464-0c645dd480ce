#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VeighNa 完整配置脚本
根据官方README.md文档进行系统配置

功能包括：
1. 检查Python环境和依赖
2. 配置数据库连接
3. 安装和配置交易接口
4. 配置数据源
5. 创建启动脚本
6. 验证配置
"""

import os
import sys
import json
import subprocess
from pathlib import Path

class VeighNaConfigurator:
    """VeighNa配置器"""
    
    def __init__(self):
        self.base_dir = Path.cwd()
        self.config_file = self.base_dir / "vt_setting.json"
        self.a_stock_config = self.base_dir / "a_stock_config.json"
        
    def check_environment(self):
        """检查Python环境"""
        print("🔍 检查Python环境...")
        print(f"Python版本: {sys.version}")
        
        # 检查Python版本
        version_info = sys.version_info
        if version_info.major != 3 or version_info.minor < 10:
            print("❌ 错误：需要Python 3.10或更高版本")
            return False
            
        print("✅ Python版本符合要求")
        
        # 检查64位
        import platform
        if platform.architecture()[0] != '64bit':
            print("❌ 错误：需要64位Python")
            return False
            
        print("✅ Python架构符合要求（64位）")
        return True
    
    def check_vnpy_installation(self):
        """检查VeighNa安装状态"""
        print("\n🔍 检查VeighNa安装状态...")
        
        try:
            import vnpy
            print(f"✅ VeighNa已安装，版本: {vnpy.__version__}")
            return True
        except ImportError:
            print("❌ VeighNa未安装")
            return False
    
    def install_vnpy_modules(self):
        """安装VeighNa核心模块"""
        print("\n📦 安装VeighNa扩展模块...")
        
        # 核心模块列表（根据README.md）
        core_modules = [
            "vnpy_ctastrategy",      # CTA策略
            "vnpy_ctabacktester",    # CTA回测
            "vnpy_datamanager",      # 数据管理
            "vnpy_riskmanager",      # 风险管理
            "vnpy_chartwizard",      # K线图表
            "vnpy_portfoliomanager", # 组合管理
            "vnpy_algotrading",      # 算法交易
            "vnpy_scripttrader",     # 脚本交易
        ]
        
        # 数据源模块
        data_modules = [
            "vnpy_akshare",          # AkShare数据
            "vnpy_tushare",          # TuShare数据
            "vnpy_rqdata",           # 米筐数据
        ]
        
        # A股交易接口
        gateway_modules = [
            "vnpy_ctp",              # CTP期货
            "vnpy_xtp",              # 中泰证券
            "vnpy_uft",              # 恒生UFT
            "vnpy_tts",              # 华泰证券
        ]
        
        all_modules = core_modules + data_modules + gateway_modules
        
        for module in all_modules:
            try:
                print(f"正在安装 {module}...")
                result = subprocess.run(
                    [sys.executable, "-m", "pip", "install", module],
                    capture_output=True,
                    text=True
                )
                
                if result.returncode == 0:
                    print(f"✅ {module} 安装成功")
                else:
                    print(f"⚠️  {module} 安装失败: {result.stderr}")
                    
            except Exception as e:
                print(f"❌ {module} 安装出错: {e}")
    
    def configure_database(self):
        """配置数据库"""
        print("\n🗄️  配置数据库...")
        
        # 默认使用SQLite（推荐新手）
        db_config = {
            "database.driver": "sqlite",
            "database.database": "database.db"
        }
        
        print("✅ 数据库配置完成（SQLite）")
        return db_config
    
    def configure_datafeed(self):
        """配置数据源"""
        print("\n📊 配置数据源...")
        
        # 默认使用AkShare（免费且功能强大）
        datafeed_config = {
            "datafeed.name": "akshare",
            "datafeed.username": "token",
            "datafeed.password": "token"
        }
        
        print("✅ 数据源配置完成（AkShare）")
        return datafeed_config
    
    def configure_logging(self):
        """配置日志"""
        print("\n📝 配置日志系统...")
        
        log_config = {
            "log.active": True,
            "log.level": 20,  # INFO级别
            "log.console": True,
            "log.file": True
        }
        
        print("✅ 日志配置完成")
        return log_config
    
    def create_main_config(self):
        """创建主配置文件"""
        print("\n⚙️  创建主配置文件...")
        
        # 合并所有配置
        config = {}
        config.update(self.configure_database())
        config.update(self.configure_datafeed())
        config.update(self.configure_logging())
        
        # 写入配置文件
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4, ensure_ascii=False)
        
        print(f"✅ 主配置文件已创建: {self.config_file}")
    
    def create_startup_script(self):
        """创建启动脚本"""
        print("\n🚀 创建启动脚本...")
        
        startup_script = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VeighNa 完整启动脚本
根据官方README.md配置生成
"""

from vnpy.event import EventEngine
from vnpy.trader.engine import MainEngine
from vnpy.trader.ui import MainWindow, create_qapp

# 导入交易接口
try:
    from vnpy_ctp import CtpGateway
    CTP_AVAILABLE = True
except ImportError:
    CTP_AVAILABLE = False
    print("⚠️  CTP接口未安装")

try:
    from vnpy_xtp import XtpGateway
    XTP_AVAILABLE = True
except ImportError:
    XTP_AVAILABLE = False
    print("⚠️  XTP接口未安装")

try:
    from vnpy_uft import UftGateway
    UFT_AVAILABLE = True
except ImportError:
    UFT_AVAILABLE = False
    print("⚠️  UFT接口未安装")

# 导入应用模块
try:
    from vnpy_ctastrategy import CtaStrategyApp
    from vnpy_ctabacktester import CtaBacktesterApp
    from vnpy_datamanager import DataManagerApp
    from vnpy_riskmanager import RiskManagerApp
    from vnpy_chartwizard import ChartWizardApp
    APPS_AVAILABLE = True
except ImportError as e:
    APPS_AVAILABLE = False
    print(f"⚠️  部分应用模块未安装: {e}")

def main():
    """启动VeighNa交易平台"""
    print("🚀 启动VeighNa交易平台...")
    print("=" * 50)
    
    # 创建Qt应用程序
    qapp = create_qapp()
    
    # 创建事件引擎
    event_engine = EventEngine()
    
    # 创建主引擎
    main_engine = MainEngine(event_engine)
    
    # 添加交易接口
    print("📡 加载交易接口...")
    if CTP_AVAILABLE:
        main_engine.add_gateway(CtpGateway)
        print("✅ CTP接口已加载")
    
    if XTP_AVAILABLE:
        main_engine.add_gateway(XtpGateway)
        print("✅ XTP接口已加载")
    
    if UFT_AVAILABLE:
        main_engine.add_gateway(UftGateway)
        print("✅ UFT接口已加载")
    
    # 添加应用模块
    if APPS_AVAILABLE:
        print("🔧 加载应用模块...")
        try:
            main_engine.add_app(CtaStrategyApp)
            main_engine.add_app(CtaBacktesterApp)
            main_engine.add_app(DataManagerApp)
            main_engine.add_app(RiskManagerApp)
            main_engine.add_app(ChartWizardApp)
            print("✅ 应用模块加载完成")
        except Exception as e:
            print(f"⚠️  部分应用模块加载失败: {e}")
    
    # 创建主窗口
    main_window = MainWindow(main_engine, event_engine)
    main_window.showMaximized()
    
    print("\n🎯 使用指南：")
    print("1. 📊 数据管理：系统 -> 数据管理")
    print("2. 📈 K线图表：应用 -> K线图表")
    print("3. 🔌 连接交易：系统 -> 连接管理")
    print("4. 📋 策略交易：应用 -> CTA策略")
    print("\n✨ VeighNa启动成功！")
    
    # 运行应用程序
    qapp.exec()

if __name__ == "__main__":
    main()
'''
        
        script_path = self.base_dir / "run_veighna.py"
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(startup_script)
        
        print(f"✅ 启动脚本已创建: {script_path}")
    
    def verify_configuration(self):
        """验证配置"""
        print("\n🔍 验证配置...")
        
        # 检查配置文件
        if self.config_file.exists():
            print("✅ 主配置文件存在")
        else:
            print("❌ 主配置文件不存在")
        
        # 检查数据库文件
        db_file = self.base_dir / "database.db"
        if db_file.exists():
            print("✅ 数据库文件存在")
        else:
            print("ℹ️  数据库文件将在首次运行时创建")
        
        # 测试导入VeighNa
        try:
            import vnpy
            print("✅ VeighNa导入成功")
        except ImportError:
            print("❌ VeighNa导入失败")
    
    def run_configuration(self):
        """运行完整配置流程"""
        print("🎯 VeighNa 完整配置开始")
        print("=" * 60)
        
        # 1. 检查环境
        if not self.check_environment():
            print("❌ 环境检查失败，请升级Python版本")
            return False
        
        # 2. 检查VeighNa安装
        if not self.check_vnpy_installation():
            print("❌ 请先安装VeighNa核心包")
            print("安装命令: pip install vnpy")
            return False
        
        # 3. 安装扩展模块
        install_modules = input("\n是否安装VeighNa扩展模块？(y/n): ").lower().strip()
        if install_modules == 'y':
            self.install_vnpy_modules()
        
        # 4. 创建配置文件
        self.create_main_config()
        
        # 5. 创建启动脚本
        self.create_startup_script()
        
        # 6. 验证配置
        self.verify_configuration()
        
        print("\n🎉 VeighNa配置完成！")
        print("=" * 40)
        print("📋 下一步操作：")
        print("1. 运行启动脚本: python run_veighna.py")
        print("2. 或使用现有脚本: python start_vnpy_enhanced.py")
        print("3. 配置交易接口（如需要）")
        print("4. 导入历史数据（如需要）")
        
        return True

def main():
    """主函数"""
    configurator = VeighNaConfigurator()
    configurator.run_configuration()

if __name__ == "__main__":
    main()