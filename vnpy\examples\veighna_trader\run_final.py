from vnpy.event import EventEngine

from vnpy.trader.engine import MainEngine
from vnpy.trader.ui import MainWindow, create_qapp

# 期货接口
from vnpy_ctp import CtpGateway

# A股接口
from vnpy_xtp import XtpGateway
from vnpy_tora import ToraStockGateway
# from vnpy_ost import OstGateway
# from vnpy_emt import EmtGateway

# 策略应用
from vnpy_ctastrategy import CtaStrategyApp
from vnpy_ctabacktester import CtaBacktesterApp

# 数据记录器（用于记录行情数据到数据库）
from vnpy_datarecorder import DataRecorderApp


def main():
    """"""
    qapp = create_qapp()

    event_engine = EventEngine()

    main_engine = MainEngine(event_engine)

    # 添加接口
    main_engine.add_gateway(CtpGateway)      # 期货接口
    main_engine.add_gateway(XtpGateway)      # A股接口1
    main_engine.add_gateway(ToraStockGateway) # A股接口2

    # 添加应用
    main_engine.add_app(CtaStrategyApp)      # CTA策略
    main_engine.add_app(CtaBacktesterApp)    # CTA回测
    main_engine.add_app(DataRecorderApp)     # 数据记录

    main_window = MainWindow(main_engine, event_engine)
    main_window.showMaximized()

    qapp.exec()


if __name__ == "__main__":
    main()