# VeighNa 量化交易平台使用指南

## 🎉 安装完成

恭喜！VeighNa 4.1.0 已成功安装在您的电脑上。

### 已安装的组件

- ✅ **VeighNa 核心框架** (v4.1.0)
- ✅ **技术分析库 TA-Lib** (v0.6.4)
- ✅ **AI量化模块** (vnpy.alpha)
- ✅ **图表模块** (vnpy.chart)
- ✅ **RPC通讯模块** (vnpy.rpc)
- ✅ **事件引擎** (vnpy.event)

## 🚀 快速开始

### 1. 基础功能测试

运行我们创建的测试脚本来验证安装：

```bash
cd f:\vn
python test_vnpy.py
```

### 2. 创建您的第一个交易程序

创建一个名为 `my_trader.py` 的文件：

```python
from vnpy.event import EventEngine
from vnpy.trader.engine import MainEngine
from vnpy.trader.ui import MainWindow, create_qapp

def main():
    """启动VeighNa交易程序"""
    # 创建Qt应用
    qapp = create_qapp()
    
    # 创建事件引擎
    event_engine = EventEngine()
    
    # 创建主引擎
    main_engine = MainEngine(event_engine)
    
    # 创建主窗口
    main_window = MainWindow(main_engine, event_engine)
    main_window.showMaximized()
    
    # 运行应用
    qapp.exec()

if __name__ == "__main__":
    main()
```

## 📊 主要功能模块

### 1. CTA策略交易

安装CTA策略模块：
```bash
pip install vnpy_ctastrategy
```

### 2. 回测分析

安装回测模块：
```bash
pip install vnpy_ctabacktester
```

### 3. 数据管理

安装数据管理模块：
```bash
pip install vnpy_datamanager
```

### 4. 交易接口

根据需要安装相应的交易接口：

- **CTP期货接口**：`pip install vnpy_ctp`
- **Interactive Brokers**：`pip install vnpy_ib`
- **中泰XTP**：`pip install vnpy_xtp`
- **华鑫奇点**：`pip install vnpy_tora`

## 🤖 AI量化功能

VeighNa 4.0新增的AI量化模块已安装，包含：

### 数据集模块 (dataset)
- 因子特征工程
- Alpha 158因子集合
- 高效批量特征计算

### 模型模块 (model)
- Lasso回归模型
- LightGBM梯度提升树
- MLP多层感知机

### 策略模块 (strategy)
- 基于ML信号的策略构建
- 截面多标的策略
- 时序单标的策略

### 投研实验室 (lab)
- 完整的投研工作流程
- 可视化分析工具
- 策略回测评估

## 📚 学习资源

### 示例代码

项目包含丰富的示例代码，位于 `examples` 目录：

- **AI量化研究**：`examples/alpha_research/`
- **CTA策略回测**：`examples/cta_backtesting/`
- **数据下载**：`examples/download_bars/`
- **图表展示**：`examples/candle_chart/`

### Jupyter Notebook示例

查看AI量化研究的Jupyter Notebook示例：

1. `download_data_rq.ipynb` - RQData数据下载
2. `research_workflow_lasso.ipynb` - Lasso回归工作流
3. `research_workflow_lgb.ipynb` - LightGBM工作流
4. `research_workflow_mlp.ipynb` - 神经网络工作流

### 官方资源

- **官方网站**：https://www.vnpy.com
- **项目文档**：https://www.vnpy.com/docs
- **社区论坛**：https://www.vnpy.com/forum
- **GitHub仓库**：https://github.com/vnpy/vnpy

## ⚙️ 配置说明

### 数据库配置

VeighNa默认使用SQLite数据库，无需额外配置。如需使用其他数据库：

- **MySQL**：`pip install vnpy_mysql`
- **PostgreSQL**：`pip install vnpy_postgresql`
- **MongoDB**：`pip install vnpy_mongodb`

### 数据源配置

支持多种数据源：

- **米筐RQData**：`pip install vnpy_rqdata`
- **迅投研**：`pip install vnpy_xt`
- **TuShare**：`pip install vnpy_tushare`
- **Wind万得**：`pip install vnpy_wind`

## 🔧 常见问题

### 1. 导入错误

如果遇到模块导入错误，请确保：
- Python版本为3.10以上
- 所有依赖包已正确安装
- 使用正确的Python环境

### 2. 图形界面问题

如果图形界面无法显示：
- 确保安装了PySide6：`pip install PySide6`
- 检查显示器设置和分辨率

### 3. 性能优化

- 使用SSD硬盘存储数据库
- 增加内存配置
- 使用多核CPU进行并行计算

## 📞 获取帮助

如果在使用过程中遇到问题：

1. 查看[官方文档](https://www.vnpy.com/docs)
2. 搜索[社区论坛](https://www.vnpy.com/forum)
3. 提交[GitHub Issue](https://github.com/vnpy/vnpy/issues)
4. 加入官方QQ群：262656087

## 🎯 下一步

建议您：

1. 阅读官方文档了解详细功能
2. 运行示例代码熟悉框架
3. 注册SimNow仿真账户进行测试
4. 开发自己的量化策略
5. 参与社区交流分享经验

---

**祝您在量化交易的道路上取得成功！** 🚀