#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VeighNa A股市场增强版启动脚本
包含更多交易接口选项和数据源配置
适用于东吴证券、招商证券等券商用户
"""

from vnpy.event import EventEngine
from vnpy.trader.engine import MainEngine
from vnpy.trader.ui import MainWindow, create_qapp

# A股交易接口
from vnpy_tts import TtsGateway          # 华泰证券 TTS
from vnpy_xtp import XtpGateway          # 中泰证券 XTP
from vnpy_uft import UftGateway          # 恒生 UFT

# 尝试导入更多交易接口（如果可用）
try:
    from vnpy_ost import OstGateway      # 东方证券 OST
    OST_AVAILABLE = True
except ImportError:
    OST_AVAILABLE = False
    print("⚠️  东方证券OST接口未安装")

try:
    from vnpy_sec import SecGateway      # 顶点飞创（多券商支持）
    SEC_AVAILABLE = True
except ImportError:
    SEC_AVAILABLE = False
    print("⚠️  顶点飞创SEC接口未安装")

# A股数据服务
from vnpy_rqdata import Datafeed as RqdataDatafeed    # 米筐数据
from vnpy_tushare import Datafeed as TushareDatafeed   # TuShare数据
from vnpy_akshare import Datafeed as AkshareDatafeed   # AkShare数据

# 应用模块
from vnpy_ctastrategy import CtaStrategyApp      # CTA策略
from vnpy_ctabacktester import CtaBacktesterApp  # CTA回测
from vnpy_datamanager import DataManagerApp      # 数据管理
from vnpy_riskmanager import RiskManagerApp      # 风险管理

def main():
    """启动VeighNa A股交易平台"""
    print("🚀 正在启动VeighNa A股交易平台（增强版）...")
    print("=" * 50)
    
    # 创建Qt应用程序
    qapp = create_qapp()
    
    # 创建事件引擎
    event_engine = EventEngine()
    
    # 创建主引擎
    main_engine = MainEngine(event_engine)
    
    # 添加A股交易接口
    print("📡 正在加载A股交易接口...")
    main_engine.add_gateway(TtsGateway)  # 华泰证券 TTS
    main_engine.add_gateway(XtpGateway)  # 中泰证券 XTP
    main_engine.add_gateway(UftGateway)  # 恒生 UFT
    
    # 添加可选接口
    if OST_AVAILABLE:
        main_engine.add_gateway(OstGateway)  # 东方证券 OST
        print("✅ 东方证券OST接口已加载")
    
    if SEC_AVAILABLE:
        main_engine.add_gateway(SecGateway)  # 顶点飞创
        print("✅ 顶点飞创SEC接口已加载")
    
    print("✅ A股交易接口加载完成")
    
    # 显示已加载的交易接口
    print("\n📋 已加载的A股交易接口：")
    print("  - 华泰证券 TTS")
    print("  - 中泰证券 XTP")
    print("  - 恒生 UFT")
    if OST_AVAILABLE:
        print("  - 东方证券 OST")
    if SEC_AVAILABLE:
        print("  - 顶点飞创 SEC")
    
    # 显示数据服务配置
    print("\n📊 已加载的数据服务：")
    print("  - 米筐数据 RQData")
    print("  - TuShare数据")
    print("  - AkShare数据 ✅")
    
    # 添加应用模块
    print("\n🔧 正在加载应用模块...")
    main_engine.add_app(CtaStrategyApp)
    main_engine.add_app(CtaBacktesterApp)
    main_engine.add_app(DataManagerApp)
    main_engine.add_app(RiskManagerApp)
    print("✅ 应用模块加载完成")
    
    # 创建主窗口
    main_window = MainWindow(main_engine, event_engine)
    main_window.showMaximized()
    
    print("\n🎯 使用指南：")
    print("=" * 30)
    print("1. 📈 查看数据：系统 -> 数据管理")
    print("2. 📊 K线图表：应用 -> K线图表")
    print("3. 🔌 连接接口：系统 -> 连接管理")
    print("4. 💼 对于东吴/招商证券用户：")
    print("   - 可使用恒生UFT接口（通用柜台）")
    print("   - 或联系券商获取专用接口")
    print("\n✨ VeighNa A股交易平台启动成功！")
    
    qapp.exec()

if __name__ == "__main__":
    main()