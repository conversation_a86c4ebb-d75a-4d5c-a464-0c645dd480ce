"""
General constant enums used in the trading platform.
"""

from enum import Enum

from .locale import _


class Direction(Enum):
    """
    Direction of order/trade/position.
    """
    LONG = _("多")
    SHORT = _("空")
    NET = _("净")


class Offset(Enum):
    """
    Offset of order/trade.
    """
    NONE = ""
    OPEN = _("开")
    CLOSE = _("平")
    CLOSETODAY = _("平今")
    CLOSEYESTERDAY = _("平昨")


class Status(Enum):
    """
    Order status.
    """
    SUBMITTING = _("提交中")
    NOTTRADED = _("未成交")
    PARTTRADED = _("部分成交")
    ALLTRADED = _("全部成交")
    CANCELLED = _("已撤销")
    REJECTED = _("拒单")


class Product(Enum):
    """
    Product class.
    """
    EQUITY = _("股票")
    FUTURES = _("期货")
    OPTION = _("期权")
    INDEX = _("指数")
    FOREX = _("外汇")
    SPOT = _("现货")
    ETF = "ETF"
    BOND = _("债券")
    WARRANT = _("权证")
    SPREAD = _("价差")
    FUND = _("基金")
    CFD = "CFD"
    SWAP = _("互换")


class OrderType(Enum):
    """
    Order type.
    """
    LIMIT = _("限价")
    MARKET = _("市价")
    STOP = "STOP"
    FAK = "FAK"
    FOK = "FOK"
    RFQ = _("询价")
    ETF = "ETF"


class OptionType(Enum):
    """
    Option type.
    """
    CALL = _("看涨期权")
    PUT = _("看跌期权")


class Exchange(Enum):
    """
    Exchange.
    """
    # Chinese - 中国交易所
    CFFEX = "CFFEX"         # China Financial Futures Exchange - 中国金融期货交易所
    SHFE = "SHFE"           # Shanghai Futures Exchange - 上海期货交易所
    CZCE = "CZCE"           # Zhengzhou Commodity Exchange - 郑州商品交易所
    DCE = "DCE"             # Dalian Commodity Exchange - 大连商品交易所
    INE = "INE"             # Shanghai International Energy Exchange - 上海国际能源交易中心
    GFEX = "GFEX"           # Guangzhou Futures Exchange - 广州期货交易所
    SSE = "SSE"             # Shanghai Stock Exchange - 上海证券交易所
    SZSE = "SZSE"           # Shenzhen Stock Exchange - 深圳证券交易所
    BSE = "BSE"             # Beijing Stock Exchange - 北京证券交易所
    SHHK = "SHHK"           # Shanghai-HK Stock Connect - 沪港通
    SZHK = "SZHK"           # Shenzhen-HK Stock Connect - 深港通
    SGE = "SGE"             # Shanghai Gold Exchange - 上海黄金交易所
    WXE = "WXE"             # Wuxi Steel Exchange - 无锡不锈钢交易中心
    CFETS = "CFETS"         # CFETS Bond Market Maker Trading System - 银行间债券市场
    XBOND = "XBOND"         # CFETS X-Bond Anonymous Trading System - 银行间债券匿名交易系统

    # Global - 国际交易所
    SMART = "SMART"         # Smart Router for US stocks - 美股智能路由
    NYSE = "NYSE"           # New York Stock Exchnage - 纽约证券交易所
    NASDAQ = "NASDAQ"       # Nasdaq Exchange - 纳斯达克交易所
    ARCA = "ARCA"           # ARCA Exchange - 纽交所高增长板
    EDGEA = "EDGEA"         # Direct Edge Exchange - 直接边缘交易所
    ISLAND = "ISLAND"       # Nasdaq Island ECN - 纳斯达克岛屿电子通讯网络
    BATS = "BATS"           # Bats Global Markets - BATS全球市场
    IEX = "IEX"             # The Investors Exchange - 投资者交易所
    AMEX = "AMEX"           # American Stock Exchange - 美国证券交易所
    TSE = "TSE"             # Toronto Stock Exchange - 多伦多证券交易所
    NYMEX = "NYMEX"         # New York Mercantile Exchange - 纽约商品交易所
    COMEX = "COMEX"         # COMEX of CME - 芝商所金属分部
    GLOBEX = "GLOBEX"       # Globex of CME - 芝商所电子交易平台
    IDEALPRO = "IDEALPRO"   # Forex ECN of Interactive Brokers - 盈透证券外汇ECN
    CME = "CME"             # Chicago Mercantile Exchange - 芝加哥商品交易所
    ICE = "ICE"             # Intercontinental Exchange - 洲际交易所
    SEHK = "SEHK"           # Stock Exchange of Hong Kong - 香港证券交易所
    HKFE = "HKFE"           # Hong Kong Futures Exchange - 香港期货交易所
    SGX = "SGX"             # Singapore Global Exchange - 新加坡交易所
    CBOT = "CBOT"           # Chicago Board of Trade - 芝加哥期货交易所
    CBOE = "CBOE"           # Chicago Board Options Exchange - 芝加哥期权交易所
    CFE = "CFE"             # CBOE Futures Exchange - 芝加哥期权交易所期货分部
    DME = "DME"             # Dubai Mercantile Exchange - 迪拜商品交易所
    EUREX = "EUX"           # Eurex Exchange - 欧洲期货交易所
    APEX = "APEX"           # Asia Pacific Exchange - 亚太交易所
    LME = "LME"             # London Metal Exchange - 伦敦金属交易所
    BMD = "BMD"             # Bursa Malaysia Derivatives - 马来西亚衍生品交易所
    TOCOM = "TOCOM"         # Tokyo Commodity Exchange - 东京商品交易所
    EUNX = "EUNX"           # Euronext Exchange - 泛欧交易所
    KRX = "KRX"             # Korean Exchange - 韩国交易所
    OTC = "OTC"             # OTC Product (Forex/CFD/Pink Sheet Equity) - 场外交易市场
    IBKRATS = "IBKRATS"     # Paper Trading Exchange of IB - 盈透证券模拟交易

    # Special Function - 特殊功能
    LOCAL = "LOCAL"         # For local generated data - 本地生成数据
    GLOBAL = "GLOBAL"       # For those exchanges not supported yet - 暂未支持的交易所


class Currency(Enum):
    """
    Currency.
    """
    USD = "USD"
    HKD = "HKD"
    CNY = "CNY"
    CAD = "CAD"


class Interval(Enum):
    """
    Interval of bar data.
    """
    MINUTE = "1m"
    HOUR = "1h"
    DAILY = "d"
    WEEKLY = "w"
    TICK = "tick"
