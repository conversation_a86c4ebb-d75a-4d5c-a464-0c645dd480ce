#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VeighNa集成版A股数据自动下载工具
直接使用VeighNa的数据管理功能批量下载所有A股数据
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta
import time
import json
from tqdm import tqdm

# 添加vnpy路径
sys.path.append(str(Path(__file__).parent / "vnpy"))

try:
    from vnpy.trader.engine import MainEngine
    from vnpy.trader.ui import MainWindow
    from vnpy.trader.setting import SETTINGS
    from vnpy.trader.object import HistoryRequest
    from vnpy.trader.constant import Exchange, Interval
    from vnpy.trader.datafeed import get_datafeed
    from vnpy.app.data_manager import DataManagerApp
    from vnpy.app.data_recorder import DataRecorderApp
except ImportError as e:
    print(f"❌ VeighNa模块导入失败: {e}")
    print("请确保VeighNa已正确安装")
    sys.exit(1)

try:
    import akshare as ak
except ImportError:
    print("❌ AkShare模块未安装，请运行: pip install akshare")
    sys.exit(1)

class VeighNaDataDownloader:
    """VeighNa集成数据下载器"""
    
    def __init__(self):
        self.main_engine = None
        self.datafeed = None
        self.success_count = 0
        self.failed_count = 0
        self.total_bars = 0
        self.failed_stocks = []
        
    def init_engine(self):
        """初始化VeighNa引擎"""
        try:
            print("正在初始化VeighNa引擎...")
            
            # 创建主引擎
            self.main_engine = MainEngine()
            
            # 添加数据管理应用
            self.main_engine.add_app(DataManagerApp)
            self.main_engine.add_app(DataRecorderApp)
            
            # 获取数据源
            self.datafeed = get_datafeed()
            if not self.datafeed:
                raise Exception("无法获取数据源，请检查配置")
            
            # 初始化数据源
            self.datafeed.init()
            
            print(f"✅ VeighNa引擎初始化成功")
            print(f"✅ 数据源: {self.datafeed.__class__.__name__}")
            return True
            
        except Exception as e:
            print(f"❌ VeighNa引擎初始化失败: {e}")
            return False
    
    def get_all_a_stocks(self):
        """获取所有A股股票列表"""
        try:
            print("正在获取A股股票列表...")
            stock_info = ak.stock_info_a_code_name()
            print(f"✅ 获取到 {len(stock_info)} 只A股股票")
            return stock_info
        except Exception as e:
            print(f"❌ 获取股票列表失败: {e}")
            return None
    
    def download_stock_history(self, symbol, exchange, start_date, end_date, interval=Interval.DAILY):
        """下载单只股票的历史数据"""
        try:
            # 创建历史数据请求
            req = HistoryRequest(
                symbol=symbol,
                exchange=exchange,
                interval=interval,
                start=start_date,
                end=end_date
            )
            
            # 查询历史数据
            bars = self.datafeed.query_bar_history(req)
            
            if bars:
                # 保存数据到数据库
                self.main_engine.database_manager.save_bar_data(bars)
                return len(bars), "成功"
            else:
                return 0, "无数据"
                
        except Exception as e:
            return 0, str(e)
    
    def batch_download(self, start_date=None, end_date=None, max_stocks=None, delay=0.1):
        """批量下载股票数据"""
        if not self.init_engine():
            return False
        
        # 设置默认日期
        if not start_date:
            start_date = datetime.now() - timedelta(days=365)  # 默认1年
        if not end_date:
            end_date = datetime.now()
        
        print(f"\n开始批量下载A股数据")
        print(f"时间范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
        print("=" * 60)
        
        # 获取股票列表
        stock_list = self.get_all_a_stocks()
        if stock_list is None:
            return False
        
        # 限制下载数量（测试用）
        if max_stocks:
            stock_list = stock_list.head(max_stocks)
            print(f"⚠️  测试模式：仅下载前 {max_stocks} 只股票")
        
        total_stocks = len(stock_list)
        print(f"准备下载 {total_stocks} 只股票的历史数据...\n")
        
        # 使用进度条批量下载
        with tqdm(total=total_stocks, desc="下载进度", unit="股") as pbar:
            for index, row in stock_list.iterrows():
                symbol = row['code']
                name = row['name']
                
                # 判断交易所
                if symbol.startswith('6'):
                    exchange = Exchange.SSE  # 上海证券交易所
                elif symbol.startswith(('0', '3')):
                    exchange = Exchange.SZSE  # 深圳证券交易所
                else:
                    continue  # 跳过其他交易所
                
                # 下载数据
                bars_count, status = self.download_stock_history(
                    symbol, exchange, start_date, end_date
                )
                
                if "成功" in status:
                    self.success_count += 1
                    self.total_bars += bars_count
                    pbar.set_postfix({
                        '成功': self.success_count,
                        '总K线': self.total_bars,
                        '当前': f"{symbol}({name})"
                    })
                else:
                    self.failed_count += 1
                    self.failed_stocks.append({
                        'symbol': symbol,
                        'name': name,
                        'exchange': exchange.value,
                        'error': status
                    })
                    pbar.set_postfix({
                        '成功': self.success_count,
                        '失败': self.failed_count,
                        '当前': f"{symbol}({name}) - 失败"
                    })
                
                pbar.update(1)
                
                # 延迟避免请求过快
                if delay > 0:
                    time.sleep(delay)
        
        return True
    
    def generate_report(self):
        """生成下载报告"""
        print("\n" + "=" * 60)
        print("📊 VeighNa数据下载完成报告")
        print("=" * 60)
        
        print(f"✅ 成功下载: {self.success_count} 只股票")
        print(f"❌ 失败下载: {self.failed_count} 只股票")
        print(f"📈 总K线数据: {self.total_bars:,} 条")
        
        if self.failed_stocks:
            print("\n❌ 失败股票列表:")
            for stock in self.failed_stocks[:10]:  # 只显示前10个
                print(f"   {stock['symbol']} ({stock['name']}) - {stock['error'][:50]}")
            if len(self.failed_stocks) > 10:
                print(f"   ... 还有 {len(self.failed_stocks) - 10} 只股票失败")
        
        # 保存失败列表
        if self.failed_stocks:
            with open("vnpy_failed_stocks.json", 'w', encoding='utf-8') as f:
                json.dump(self.failed_stocks, f, ensure_ascii=False, indent=2)
            print(f"\n💾 失败股票列表已保存到: vnpy_failed_stocks.json")
        
        print(f"\n💾 数据已保存到VeighNa数据库")
        print(f"📁 数据库位置: {SETTINGS.get('database.database', 'database.db')}")
    
    def cleanup(self):
        """清理资源"""
        if self.main_engine:
            self.main_engine.close()

def create_quick_download_script():
    """创建快速下载脚本"""
    script_content = '''
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
快速下载常用股票数据
下载热门股票的历史数据，适合快速测试
"""

from VeighNa自动下载A股数据 import VeighNaDataDownloader
from datetime import datetime, timedelta
from vnpy.trader.constant import Exchange

def download_popular_stocks():
    """下载热门股票数据"""
    
    # 热门股票列表
    popular_stocks = [
        ('000001', 'SZSE', '平安银行'),
        ('000002', 'SZSE', '万科A'),
        ('000858', 'SZSE', '五粮液'),
        ('000333', 'SZSE', '美的集团'),
        ('600000', 'SSE', '浦发银行'),
        ('600036', 'SSE', '招商银行'),
        ('600519', 'SSE', '贵州茅台'),
        ('600887', 'SSE', '伊利股份'),
        ('000725', 'SZSE', '京东方A'),
        ('002415', 'SZSE', '海康威视'),
    ]
    
    downloader = VeighNaDataDownloader()
    
    if not downloader.init_engine():
        return
    
    start_date = datetime.now() - timedelta(days=180)  # 最近6个月
    end_date = datetime.now()
    
    print("开始下载热门股票数据...")
    
    for symbol, exchange_str, name in popular_stocks:
        exchange = Exchange.SSE if exchange_str == 'SSE' else Exchange.SZSE
        
        print(f"正在下载 {symbol} ({name})...")
        bars_count, status = downloader.download_stock_history(
            symbol, exchange, start_date, end_date
        )
        
        if "成功" in status:
            print(f"✅ {symbol} 下载成功，获取 {bars_count} 条数据")
            downloader.success_count += 1
            downloader.total_bars += bars_count
        else:
            print(f"❌ {symbol} 下载失败: {status}")
            downloader.failed_count += 1
    
    downloader.generate_report()
    downloader.cleanup()

if __name__ == "__main__":
    download_popular_stocks()
'''
    
    with open("快速下载热门股票.py", 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print("✅ 快速下载脚本已创建: 快速下载热门股票.py")

def main():
    """主函数"""
    print("🚀 VeighNa集成版A股数据自动下载工具")
    print("=" * 60)
    
    print("请选择下载模式:")
    print("1. 测试模式 (下载前20只股票，最近1个月数据)")
    print("2. 快速模式 (下载前100只股票，最近3个月数据)")
    print("3. 标准模式 (下载所有股票，最近1年数据)")
    print("4. 完整模式 (下载所有股票，最近3年数据)")
    print("5. 热门股票模式 (下载10只热门股票)")
    print("6. 自定义模式")
    
    try:
        choice = input("\n请输入选择 (1-6): ").strip()
        
        downloader = VeighNaDataDownloader()
        
        if choice == "1":
            # 测试模式
            start_date = datetime.now() - timedelta(days=30)
            downloader.batch_download(
                start_date=start_date,
                max_stocks=20,
                delay=0.2
            )
            
        elif choice == "2":
            # 快速模式
            start_date = datetime.now() - timedelta(days=90)
            downloader.batch_download(
                start_date=start_date,
                max_stocks=100,
                delay=0.1
            )
            
        elif choice == "3":
            # 标准模式
            start_date = datetime.now() - timedelta(days=365)
            downloader.batch_download(
                start_date=start_date,
                delay=0.1
            )
            
        elif choice == "4":
            # 完整模式
            start_date = datetime.now() - timedelta(days=1095)
            downloader.batch_download(
                start_date=start_date,
                delay=0.05
            )
            
        elif choice == "5":
            # 热门股票模式
            create_quick_download_script()
            print("\n请运行: python 快速下载热门股票.py")
            return
            
        elif choice == "6":
            # 自定义模式
            days = int(input("请输入下载天数 (如: 365): "))
            max_stocks = input("限制股票数量 (回车下载全部): ").strip()
            max_stocks = int(max_stocks) if max_stocks else None
            
            start_date = datetime.now() - timedelta(days=days)
            downloader.batch_download(
                start_date=start_date,
                max_stocks=max_stocks,
                delay=0.1
            )
            
        else:
            print("❌ 无效选择")
            return
        
        # 生成报告
        downloader.generate_report()
        
        # 清理资源
        downloader.cleanup()
        
        print("\n🎉 数据下载完成！")
        print("\n📋 后续操作:")
        print("1. 启动VeighNa查看下载的数据")
        print("2. 在VeighNa的数据管理界面查看数据统计")
        print("3. 可以开始使用策略回测功能")
        
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断下载")
        if 'downloader' in locals():
            downloader.cleanup()
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        if 'downloader' in locals():
            downloader.cleanup()

if __name__ == "__main__":
    main()