# VeighNa 启动指南

## 启动方式概览

VeighNa提供了多种启动方式，您可以根据需要选择合适的方法：

### 1. 使用自定义启动脚本（推荐）

我们已经为您创建了一个简单的启动脚本 `start_vnpy.py`：

```bash
python start_vnpy.py
```

这个脚本会启动VeighNa的图形界面，包含基本的交易功能。

### 2. 使用官方示例启动

进入官方示例目录并运行：

```bash
cd vnpy/examples/veighna_trader
python run.py
```

### 3. 使用VeighNa Station（如果已安装）

如果您安装了VeighNa Station，可以直接通过图形界面启动。

### 4. 在Python脚本中启动

```python
from vnpy.event import EventEngine
from vnpy.trader.engine import MainEngine
from vnpy.trader.ui import MainWindow, create_qapp

# 创建应用程序
qapp = create_qapp()
event_engine = EventEngine()
main_engine = MainEngine(event_engine)
main_window = MainWindow(main_engine, event_engine)
main_window.showMaximized()

# 运行
qapp.exec()
```

## 扩展功能安装

### 交易接口（Gateway）

如需连接实际的交易所或券商，需要安装相应的交易接口：

```bash
# CTP接口（期货）
pip install vnpy_ctp

# Interactive Brokers接口
pip install vnpy_ib

# 其他接口
pip install vnpy_tts      # 华泰证券
pip install vnpy_xtp      # 中泰证券
pip install vnpy_oes      # 东方证券
```

### 应用模块（App）

安装策略和分析应用：

```bash
# CTA策略模块
pip install vnpy_ctastrategy

# 回测模块
pip install vnpy_ctabacktester

# 数据管理模块
pip install vnpy_datamanager

# 风险管理模块
pip install vnpy_riskmanager

# 算法交易模块
pip install vnpy_algotrading
```

## 启动后的操作

1. **连接交易接口**：在"系统"菜单中选择"连接"，配置您的交易账户
2. **数据管理**：使用数据管理模块下载和管理历史数据
3. **策略开发**：使用CTA策略模块开发和运行交易策略
4. **回测分析**：使用回测模块测试策略效果

## 常见问题

### Q: 启动后界面空白或无响应
A: 检查Python环境和依赖是否正确安装，尝试重新安装PySide6

### Q: 无法连接交易接口
A: 确保已安装相应的Gateway扩展包，并正确配置账户信息

### Q: 缺少某些功能模块
A: 根据需要安装相应的App扩展包

## 下一步

- 阅读 `VeighNa使用指南.md` 了解详细功能
- 查看 `vnpy/examples/` 目录下的示例代码
- 访问官方文档：https://www.vnpy.com
- 加入社区论坛获取支持

---

**提示**：首次使用建议先在模拟环境中测试，熟悉平台功能后再连接实盘账户。