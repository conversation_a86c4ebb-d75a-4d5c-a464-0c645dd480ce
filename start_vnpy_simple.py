#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VeighNa 简化启动脚本
专门用于查看股票数据，避免复杂的模块导入问题
"""

from vnpy.event import EventEngine
from vnpy.trader.engine import MainEngine
from vnpy.trader.ui import MainWindow, create_qapp

# 基础应用模块
from vnpy_chartwizard import ChartWizardApp  # K线图表

def main():
    """启动VeighNa简化版"""
    print("正在启动VeighNa简化版（专用于查看数据）...")
    
    # 创建Qt应用程序
    qapp = create_qapp()
    
    # 创建事件引擎
    event_engine = EventEngine()
    
    # 创建主引擎
    main_engine = MainEngine(event_engine)
    
    # 添加K线图表应用
    print("正在加载K线图表模块...")
    try:
        main_engine.add_app(ChartWizardApp)
        print("✅ K线图表模块加载成功")
    except Exception as e:
        print(f"⚠️  K线图表模块加载失败: {e}")
    
    # 创建主窗口
    main_window = MainWindow(main_engine, event_engine)
    main_window.showMaximized()
    
    print("="*50)
    print("VeighNa简化版启动成功！")
    print("="*50)
    print("使用说明：")
    print("1. 点击 '应用' -> 'K线图表'")
    print("2. 输入股票代码: 000001.SZSE")
    print("3. 选择周期: 日线")
    print("4. 点击 '查询' 查看K线图")
    print("="*50)
    print("可用的股票代码示例：")
    print("  - 000001.SZSE (平安银行)")
    print("  - 000002.SZSE (万科A)")
    print("  - 600000.SSE (浦发银行)")
    print("  - 600036.SSE (招商银行)")
    print("="*50)
    
    # 运行应用程序
    qapp.exec()

if __name__ == "__main__":
    main()
