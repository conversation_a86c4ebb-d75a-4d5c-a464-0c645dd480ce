#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查VeighNa数据源连接状态
诊断VeighNa中AkShare数据源的配置和连接问题
"""

import sys
import os
import json
from pathlib import Path

def check_vnpy_installation():
    """检查VeighNa安装状态"""
    print("=== VeighNa安装检查 ===")
    
    try:
        import vnpy
        print(f"✅ VeighNa已安装，版本: {vnpy.__version__}")
        
        from vnpy.trader.engine import MainEngine
        print("✅ MainEngine导入成功")
        
        from vnpy.trader.ui import MainWindow
        print("✅ MainWindow导入成功")
        
        return True
    except ImportError as e:
        print(f"❌ VeighNa导入失败: {e}")
        return False

def check_akshare_datafeed():
    """检查AkShare数据源"""
    print("\n=== AkShare数据源检查 ===")
    
    try:
        # 检查vnpy_akshare模块
        import vnpy_akshare
        print(f"✅ vnpy_akshare模块已安装")
        
        from vnpy_akshare import AkshareDatafeed
        print("✅ AkshareDatafeed类导入成功")
        
        # 检查akshare模块
        import akshare as ak
        print("✅ akshare模块可用")
        
        return True
    except ImportError as e:
        print(f"❌ AkShare相关模块导入失败: {e}")
        return False

def check_vt_setting():
    """检查vt_setting.json配置"""
    print("\n=== 配置文件检查 ===")
    
    setting_file = Path("vt_setting.json")
    
    if not setting_file.exists():
        print("❌ vt_setting.json文件不存在")
        return False
    
    try:
        with open(setting_file, 'r', encoding='utf-8') as f:
            settings = json.load(f)
        
        print("✅ vt_setting.json文件读取成功")
        
        # 检查数据源配置
        datafeed_name = settings.get('datafeed.name')
        datafeed_username = settings.get('datafeed.username')
        datafeed_password = settings.get('datafeed.password')
        
        print(f"数据源名称: {datafeed_name}")
        print(f"用户名: {datafeed_username}")
        print(f"密码: {datafeed_password}")
        
        if datafeed_name == 'akshare':
            print("✅ 数据源配置为AkShare")
            return True
        else:
            print(f"❌ 数据源配置错误，当前为: {datafeed_name}")
            return False
            
    except Exception as e:
        print(f"❌ 配置文件读取失败: {e}")
        return False

def test_datafeed_connection():
    """测试数据源连接"""
    print("\n=== 数据源连接测试 ===")
    
    try:
        from vnpy.trader.engine import MainEngine
        from vnpy.trader.setting import SETTINGS
        from vnpy.trader.datafeed import get_datafeed
        
        # 创建主引擎（无UI模式）
        main_engine = MainEngine()
        print("✅ MainEngine创建成功")
        
        # 获取数据源
        datafeed = get_datafeed()
        if datafeed:
            print(f"✅ 数据源获取成功: {datafeed.__class__.__name__}")
            
            # 测试数据源初始化
            try:
                datafeed.init()
                print("✅ 数据源初始化成功")
                
                # 测试查询合约
                from vnpy.trader.object import HistoryRequest
                from vnpy.trader.constant import Exchange, Interval
                from datetime import datetime, timedelta
                
                # 创建历史数据请求
                req = HistoryRequest(
                    symbol="000001",
                    exchange=Exchange.SZSE,
                    interval=Interval.DAILY,
                    start=datetime.now() - timedelta(days=30),
                    end=datetime.now()
                )
                
                print("正在测试历史数据查询...")
                bars = datafeed.query_bar_history(req)
                
                if bars:
                    print(f"✅ 历史数据查询成功，获取到 {len(bars)} 条数据")
                    print(f"最新数据时间: {bars[-1].datetime}")
                    print(f"最新收盘价: {bars[-1].close_price}")
                    return True
                else:
                    print("❌ 历史数据查询返回空结果")
                    return False
                    
            except Exception as e:
                print(f"❌ 数据源测试失败: {e}")
                return False
        else:
            print("❌ 无法获取数据源")
            return False
            
    except Exception as e:
        print(f"❌ 数据源连接测试失败: {e}")
        return False

def check_database_config():
    """检查数据库配置"""
    print("\n=== 数据库配置检查 ===")
    
    try:
        with open("vt_setting.json", 'r', encoding='utf-8') as f:
            settings = json.load(f)
        
        db_driver = settings.get('database.driver')
        db_database = settings.get('database.database')
        
        print(f"数据库驱动: {db_driver}")
        print(f"数据库文件: {db_database}")
        
        if db_driver == 'sqlite':
            db_file = Path(db_database)
            if db_file.exists():
                print(f"✅ 数据库文件存在: {db_file.absolute()}")
                print(f"文件大小: {db_file.stat().st_size} 字节")
            else:
                print(f"⚠️  数据库文件不存在，将自动创建: {db_file.absolute()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库配置检查失败: {e}")
        return False

def provide_solutions():
    """提供解决方案"""
    print("\n=== 解决方案 ===")
    
    print("🔧 如果数据下载仍然失败，请尝试以下解决方案:")
    print("")
    print("1. 重启VeighNa:")
    print("   - 完全关闭VeighNa程序")
    print("   - 重新运行: python start_vnpy_a_stock.py")
    print("")
    print("2. 检查网络连接:")
    print("   - 确保网络连接正常")
    print("   - 检查防火墙设置")
    print("")
    print("3. 更新日期设置:")
    print("   - 开始日期: 2025/07/22")
    print("   - 结束日期: 留空（自动使用今天）")
    print("")
    print("4. 尝试不同的股票代码:")
    print("   - 000001 (平安银行)")
    print("   - 000002 (万科A)")
    print("   - 600000 (浦发银行)")
    print("")
    print("5. 检查VeighNa日志:")
    print("   - 查看VeighNa界面底部的日志信息")
    print("   - 寻找错误提示")

def main():
    """主函数"""
    print("VeighNa数据源连接诊断工具")
    print("=" * 50)
    
    # 检查各个组件
    vnpy_ok = check_vnpy_installation()
    akshare_ok = check_akshare_datafeed()
    setting_ok = check_vt_setting()
    db_ok = check_database_config()
    
    # 如果基础组件都正常，测试连接
    if vnpy_ok and akshare_ok and setting_ok:
        connection_ok = test_datafeed_connection()
    else:
        connection_ok = False
    
    print("\n=== 诊断结果 ===")
    print(f"VeighNa安装: {'✅' if vnpy_ok else '❌'}")
    print(f"AkShare数据源: {'✅' if akshare_ok else '❌'}")
    print(f"配置文件: {'✅' if setting_ok else '❌'}")
    print(f"数据库配置: {'✅' if db_ok else '❌'}")
    print(f"数据源连接: {'✅' if connection_ok else '❌'}")
    
    if all([vnpy_ok, akshare_ok, setting_ok, db_ok, connection_ok]):
        print("\n🎉 所有检查都通过！数据源应该可以正常工作。")
        print("如果仍然无法下载数据，可能是临时网络问题，请稍后重试。")
    else:
        print("\n⚠️  发现问题，请查看上面的详细信息。")
    
    # 提供解决方案
    provide_solutions()

if __name__ == "__main__":
    main()