# VeighNa A股市场配置指南

## 概述

本指南将帮助您配置VeighNa平台以进行中国A股市场的量化交易。我们已经为您安装了完整的A股交易生态系统。

## 已安装的组件

### 🏦 交易接口（Gateway）

| 接口名称 | 券商 | 支持产品 | 特点 |
|---------|------|----------|------|
| **TTS** | 华泰证券 | 股票、ETF、可转债 | 稳定可靠，功能全面 |
| **XTP** | 中泰证券 | 股票、ETF、期权 | 极速交易，低延迟 |
| **UFT** | 恒生 | 股票、基金、债券 | 专业平台，机构首选 |

### 📊 数据服务（Data Service）

| 服务名称 | 提供商 | 数据类型 | 费用 |
|---------|--------|----------|------|
| **RQData** | 米筐 | 股票行情、财务数据、指数 | 付费 |
| **TuShare** | TuShare | 股票行情、基本面、宏观 | 免费/付费 |

### 🛠️ 应用模块（App）

- **CTA策略模块**：开发和运行量化策略
- **CTA回测模块**：历史数据回测验证
- **数据管理模块**：数据下载和管理
- **风险管理模块**：实时风险控制

## 快速启动

### 1. 启动A股专用平台

```bash
python start_vnpy_a_stock.py
```

这将启动包含所有A股组件的完整平台。

### 2. 配置交易接口

#### 华泰证券 TTS 配置

1. 在主界面点击"系统" → "连接" → "TTS"
2. 填写以下信息：
   - 用户名：您的华泰证券账户
   - 密码：交易密码
   - 营业部代码：咨询华泰证券
   - 服务器地址：咨询华泰证券
   - 服务器端口：咨询华泰证券

#### 中泰证券 XTP 配置

1. 在主界面点击"系统" → "连接" → "XTP"
2. 填写以下信息：
   - 账户：您的中泰证券账户
   - 密码：交易密码
   - 客户号：咨询中泰证券
   - 交易服务器：咨询中泰证券
   - 行情服务器：咨询中泰证券

### 3. 配置数据服务

#### 米筐数据 RQData

1. 注册米筐数据账户：https://www.ricequant.com/
2. 在VeighNa中配置：
   - 用户名：米筐账户用户名
   - 密码：米筐账户密码

#### TuShare数据

1. 注册TuShare账户：https://tushare.pro/
2. 获取API Token
3. 在VeighNa中配置Token

## 使用流程

### 第一步：数据准备

1. **启动数据管理模块**
   - 在主界面点击"功能" → "数据管理"
   
2. **下载历史数据**
   - 选择股票代码（如：000001.SZ）
   - 设置时间范围
   - 选择数据频率（1分钟、5分钟、日线等）
   - 点击"下载"开始获取数据

### 第二步：策略开发

1. **创建策略文件**
   ```python
   from vnpy_ctastrategy import (
       CtaTemplate,
       StopOrder,
       TickData,
       BarData,
       TradeData,
       OrderData,
       BarGenerator,
       ArrayManager,
   )
   
   class DualMaStrategy(CtaTemplate):
       """双均线策略"""
       
       author = "VeighNa Team"
       
       fast_window = 10
       slow_window = 20
       fixed_size = 100
       
       # 策略逻辑实现...
   ```

2. **在CTA策略模块中加载策略**
   - 点击"功能" → "CTA策略"
   - 添加策略实例
   - 设置参数
   - 启动策略

### 第三步：回测验证

1. **使用CTA回测模块**
   - 点击"功能" → "CTA回测"
   - 选择策略和参数
   - 设置回测时间范围
   - 运行回测并查看结果

### 第四步：实盘交易

1. **风险管理设置**
   - 启用风险管理模块
   - 设置最大持仓、止损等参数
   
2. **连接交易接口**
   - 确保账户资金充足
   - 连接对应的券商接口
   
3. **启动策略**
   - 在CTA策略模块中启动实盘策略
   - 监控策略运行状态

## 常用策略模板

### 1. 双均线策略
- **原理**：快线上穿慢线买入，下穿卖出
- **参数**：快线周期10，慢线周期20
- **适用**：趋势性较强的股票

### 2. 布林带策略
- **原理**：价格触及下轨买入，上轨卖出
- **参数**：周期20，标准差2.0
- **适用**：震荡行情

### 3. RSI策略
- **原理**：RSI超卖买入，超买卖出
- **参数**：周期14，买入30，卖出70
- **适用**：个股短线交易

## 风险提示

⚠️ **重要提醒**：

1. **模拟交易优先**：建议先在模拟环境中充分测试
2. **资金管理**：严格控制单笔交易和总持仓风险
3. **止损设置**：必须设置合理的止损点
4. **监控运行**：实盘运行时需要持续监控
5. **合规交易**：遵守相关法律法规和交易所规则

## 技术支持

- **官方文档**：https://www.vnpy.com
- **社区论坛**：https://forum.vnpy.com
- **GitHub**：https://github.com/vnpy/vnpy
- **配置文件**：参考 `a_stock_config.json`

## 下一步

1. 阅读策略开发文档
2. 学习回测分析方法
3. 了解风险管理原理
4. 参与社区讨论交流

---

**祝您交易顺利！** 📈