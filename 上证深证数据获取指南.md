# 上证深证数据获取指南

## 概述

本指南将详细说明如何在VeighNa平台中配置和获取上海证券交易所（SSE）和深圳证券交易所（SZSE）的股票数据。

## 交易所代码说明

- **SSE**：上海证券交易所（Shanghai Stock Exchange）
- **SZSE**：深圳证券交易所（Shenzhen Stock Exchange）

## 可用数据源

### 1. AkShare数据源（推荐 - 免费）

**优势：**
- 完全免费
- 支持上证和深证所有股票
- 数据更新及时
- 无需注册账户

**配置方法：**
1. 启动VeighNa平台：`python start_vnpy_a_stock.py`
2. 在主界面点击"系统" → "配置"
3. 找到"数据服务"配置项
4. 设置以下参数：
   ```
   datafeed.name = "akshare"
   datafeed.username = "token"
   datafeed.password = "token"
   ```
5. 保存配置并重启平台

### 2. TuShare数据源

**优势：**
- 数据质量高
- 支持历史数据
- 提供基本面数据

**配置方法：**
1. 注册TuShare账户：https://tushare.pro/
2. 获取API Token
3. 在VeighNa配置：
   ```
   datafeed.name = "tushare"
   datafeed.username = "your_username"
   datafeed.password = "your_token"
   ```

### 3. 米筐RQData数据源

**优势：**
- 专业级数据服务
- 数据准确性高
- 支持多种数据类型

**配置方法：**
1. 注册米筐账户：https://www.ricequant.com/
2. 在VeighNa配置：
   ```
   datafeed.name = "rqdata"
   datafeed.username = "your_username"
   datafeed.password = "your_password"
   ```

## 数据获取步骤

### 第一步：配置数据源

选择上述任一数据源进行配置（推荐AkShare免费版）。

### 第二步：下载历史数据

1. **启动数据管理模块**
   - 在主界面点击"功能" → "数据管理"

2. **下载上证股票数据**
   ```
   股票代码格式：600000.SSE（如：600000.SSE 浦发银行）
   交易所：SSE
   时间范围：根据需要设置
   数据频率：1分钟/5分钟/日线等
   ```

3. **下载深证股票数据**
   ```
   股票代码格式：000001.SZSE（如：000001.SZSE 平安银行）
   交易所：SZSE
   时间范围：根据需要设置
   数据频率：1分钟/5分钟/日线等
   ```

### 第三步：实时行情订阅

1. **在交易界面订阅行情**
   - 交易所选择：SSE 或 SZSE
   - 输入股票代码（如：600000 或 000001）
   - 按回车键订阅

2. **批量订阅示例**
   ```python
   # 上证股票
   symbols_sse = ["600000.SSE", "600036.SSE", "600519.SSE"]
   
   # 深证股票
   symbols_szse = ["000001.SZSE", "000002.SZSE", "000858.SZSE"]
   
   # 在策略中订阅
   for symbol in symbols_sse + symbols_szse:
       self.subscribe(symbol)
   ```

## 常用股票代码示例

### 上证主要股票（SSE）

| 股票名称 | 代码 | VeighNa格式 |
|---------|------|-------------|
| 浦发银行 | 600000 | 600000.SSE |
| 招商银行 | 600036 | 600036.SSE |
| 贵州茅台 | 600519 | 600519.SSE |
| 中国平安 | 601318 | 601318.SSE |
| 工商银行 | 601398 | 601398.SSE |

### 深证主要股票（SZSE）

| 股票名称 | 代码 | VeighNa格式 |
|---------|------|-------------|
| 平安银行 | 000001 | 000001.SZSE |
| 万科A | 000002 | 000002.SZSE |
| 五粮液 | 000858 | 000858.SZSE |
| 美的集团 | 000333 | 000333.SZSE |
| 格力电器 | 000651 | 000651.SZSE |

## 数据使用示例

### 在策略中使用数据

```python
from vnpy_ctastrategy import CtaTemplate

class SSESZSEStrategy(CtaTemplate):
    """上证深证股票策略示例"""
    
    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)
        
        # 订阅上证和深证股票
        self.sse_symbols = ["600000.SSE", "600036.SSE"]
        self.szse_symbols = ["000001.SZSE", "000002.SZSE"]
        
        for symbol in self.sse_symbols + self.szse_symbols:
            self.subscribe(symbol)
    
    def on_tick(self, tick):
        """处理实时行情数据"""
        if tick.vt_symbol.endswith(".SSE"):
            # 处理上证股票数据
            self.write_log(f"上证股票 {tick.vt_symbol} 最新价: {tick.last_price}")
        elif tick.vt_symbol.endswith(".SZSE"):
            # 处理深证股票数据
            self.write_log(f"深证股票 {tick.vt_symbol} 最新价: {tick.last_price}")
```

## 故障排除

### 常见问题

1. **数据源连接失败**
   - 检查网络连接
   - 验证配置参数是否正确
   - 确认数据源服务状态

2. **股票代码格式错误**
   - 上证股票：6位数字.SSE
   - 深证股票：6位数字.SZSE
   - 注意大小写敏感

3. **历史数据下载失败**
   - 检查时间范围设置
   - 确认股票代码存在
   - 验证数据源权限

### 技术支持

如遇到问题，可以：
1. 查看VeighNa日志输出
2. 运行测试脚本：`python test_a_stock_modules.py`
3. 参考官方文档：https://www.vnpy.com/docs/

## 下一步

配置完成后，您可以：
1. 开发基于上证深证数据的量化策略
2. 进行历史数据回测
3. 实施实时交易策略
4. 分析市场数据和趋势

祝您交易顺利！