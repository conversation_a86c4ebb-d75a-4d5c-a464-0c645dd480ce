#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VeighNa 无界面启动脚本
适用于服务器环境或自动化交易场景
"""

import time
import signal
import sys
from vnpy.event import EventEngine
from vnpy.trader.engine import MainEngine
from vnpy.trader.object import LogData
from vnpy.trader.utility import load_json, save_json

class HeadlessTrader:
    """无界面交易器"""
    
    def __init__(self):
        self.event_engine = EventEngine()
        self.main_engine = MainEngine(self.event_engine)
        self.running = True
        
        # 注册事件监听
        self.event_engine.register("eLog", self.process_log_event)
        
        # 注册信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def process_log_event(self, event):
        """处理日志事件"""
        log: LogData = event.data
        print(f"[{log.time}] {log.level}: {log.msg}")
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n收到信号 {signum}，正在关闭...")
        self.stop()
    
    def start(self):
        """启动交易器"""
        print("启动VeighNa无界面交易器...")
        
        # 添加交易接口（如果需要）
        # self.add_gateways()
        
        # 添加应用模块（如果需要）
        # self.add_apps()
        
        print("交易器启动完成")
        print("按 Ctrl+C 退出")
        
        # 主循环
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            self.stop()
    
    def add_gateways(self):
        """添加交易接口"""
        # 示例：添加CTP接口
        # from vnpy_ctp import CtpGateway
        # self.main_engine.add_gateway(CtpGateway)
        # print("已添加CTP交易接口")
        pass
    
    def add_apps(self):
        """添加应用模块"""
        # 示例：添加CTA策略应用
        # from vnpy_ctastrategy import CtaStrategyApp
        # self.main_engine.add_app(CtaStrategyApp)
        # print("已添加CTA策略应用")
        pass
    
    def connect_gateway(self, gateway_name: str, setting: dict):
        """连接交易接口"""
        self.main_engine.connect(setting, gateway_name)
        print(f"正在连接 {gateway_name}...")
    
    def stop(self):
        """停止交易器"""
        print("正在关闭交易器...")
        self.running = False
        
        # 断开所有连接
        for gateway_name in self.main_engine.get_all_gateway_names():
            self.main_engine.close(gateway_name)
        
        # 停止引擎
        self.main_engine.close()
        print("交易器已关闭")
        sys.exit(0)

def main():
    """主函数"""
    trader = HeadlessTrader()
    trader.start()

if __name__ == "__main__":
    main()