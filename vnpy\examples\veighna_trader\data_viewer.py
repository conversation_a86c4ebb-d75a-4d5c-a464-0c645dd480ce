"""
数据查看工具
用于查看本地CSV格式的股票数据
"""

import csv
import os
from datetime import datetime
from typing import List, Dict

def load_csv_data(file_path: str) -> List[Dict]:
    """
    加载CSV格式的数据
    """
    data = []
    if not os.path.exists(file_path):
        print(f"文件 {file_path} 不存在")
        return data
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                data.append(row)
    except Exception as e:
        print(f"读取文件出错: {e}")
    
    return data

def display_data_info(data: List[Dict], file_path: str):
    """
    显示数据基本信息
    """
    if not data:
        print("没有数据可显示")
        return
    
    print(f"文件: {file_path}")
    print(f"数据条数: {len(data)}")
    print("列名:", list(data[0].keys()))
    
    if data:
        print("\n前5条数据:")
        for i, row in enumerate(data[:5]):
            print(f"{i+1}. {row}")

def main():
    """
    主函数
    """
    # 查找当前目录下的CSV文件
    current_dir = os.path.dirname(os.path.abspath(__file__))
    csv_files = [f for f in os.listdir(current_dir) if f.endswith('.csv')]
    
    if not csv_files:
        print("当前目录下没有找到CSV文件")
        # 检查上级目录
        parent_dir = os.path.dirname(current_dir)
        csv_files = [f for f in os.listdir(parent_dir) if f.endswith('.csv')]
        if csv_files:
            print(f"在上级目录找到CSV文件: {csv_files[:3]}...")
    
    print("VeighNa数据查看工具")
    print("=" * 50)
    
    # 如果有已下载的股票列表文件
    stock_list_file = r"F:\vn\已下载股票列表.csv"
    if os.path.exists(stock_list_file):
        print("发现已下载股票列表文件:")
        data = load_csv_data(stock_list_file)
        display_data_info(data, stock_list_file)
    
    print("\n" + "=" * 50)
    print("提示: 您可以将CSV格式的股票数据放在项目目录中，然后通过VeighNa的数据管理功能导入和查看。")

if __name__ == "__main__":
    main()