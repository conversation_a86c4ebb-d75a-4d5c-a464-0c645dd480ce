#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试股票代码是否有效
"""

import akshare as ak
import pandas as pd

def test_stock_code(symbol):
    """测试股票代码"""
    print(f"🔍 测试股票代码: {symbol}")
    
    try:
        # 获取股票数据
        data = ak.stock_zh_a_hist(
            symbol=symbol, 
            period='daily', 
            start_date='20241201', 
            end_date='20241210'
        )
        
        if data is not None and not data.empty:
            print(f"✅ {symbol} 有效，获取到 {len(data)} 条数据")
            print(f"   最新日期: {data.iloc[0]['日期']}")
            print(f"   收盘价: {data.iloc[0]['收盘']}")
            return True
        else:
            print(f"❌ {symbol} 无数据")
            return False
            
    except Exception as e:
        print(f"❌ {symbol} 错误: {e}")
        return False

def get_valid_stocks():
    """获取有效的股票代码"""
    print("\n📋 获取有效的股票代码列表")
    
    try:
        # 获取A股代码表
        stock_list = ak.stock_info_a_code_name()
        
        if not stock_list.empty:
            print(f"✅ 获取到 {len(stock_list)} 只A股")
            
            # 显示一些常见股票
            common_stocks = ['600000', '600036', '000001', '000002', '600519']
            
            print("\n🏢 常见股票代码:")
            for code in common_stocks:
                stock_info = stock_list[stock_list['code'] == code]
                if not stock_info.empty:
                    name = stock_info.iloc[0]['name']
                    print(f"   {code} - {name}")
            
            return stock_list
        else:
            print("❌ 无法获取股票代码表")
            return None
            
    except Exception as e:
        print(f"❌ 获取股票列表失败: {e}")
        return None

def main():
    """主函数"""
    print("🔍 股票代码有效性测试")
    print("=" * 40)
    
    # 测试600580
    test_stock_code('600580')
    
    # 测试一些常见股票
    common_stocks = ['600000', '600036', '000001', '000002']
    
    print(f"\n🧪 测试常见股票代码:")
    for stock in common_stocks:
        test_stock_code(stock)
    
    # 获取有效股票列表
    get_valid_stocks()
    
    print("\n💡 建议:")
    print("1. 如果600580无效，请使用其他有效的股票代码")
    print("2. 推荐使用: 600000, 600036, 000001, 000002")
    print("3. 确保网络连接正常")

if __name__ == "__main__":
    main()
