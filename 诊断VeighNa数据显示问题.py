#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
诊断VeighNa数据显示问题
检查为什么在VeighNa中看不到下载的数据
"""

import sqlite3
import pandas as pd
import os
from datetime import datetime
from pathlib import Path

def check_database_files():
    """检查数据库文件状态"""
    print("🔍 检查数据库文件状态")
    print("=" * 50)
    
    # 检查各个数据库文件
    db_files = {
        "VeighNa主数据库": "f:/vn/database.db",
        "优化版下载数据": "f:/vn/optimized_stock_data.db",
        "原始下载数据": "f:/vn/stock_data.db"
    }
    
    for name, path in db_files.items():
        print(f"\n📁 {name}: {path}")
        
        if os.path.exists(path):
            try:
                # 获取文件大小
                size_mb = os.path.getsize(path) / (1024 * 1024)
                print(f"   ✅ 文件存在，大小: {size_mb:.2f} MB")
                
                # 检查表结构
                conn = sqlite3.connect(path)
                tables = pd.read_sql("SELECT name FROM sqlite_master WHERE type='table'", conn)
                print(f"   📋 数据表: {', '.join(tables['name'].tolist())}")
                
                # 检查数据量
                for table in tables['name']:
                    try:
                        count = pd.read_sql(f"SELECT COUNT(*) as count FROM {table}", conn)['count'].iloc[0]
                        print(f"   📊 {table}: {count:,} 条记录")
                    except:
                        print(f"   ❌ {table}: 无法读取")
                
                conn.close()
                
            except Exception as e:
                print(f"   ❌ 读取失败: {e}")
        else:
            print(f"   ❌ 文件不存在")

def check_vnpy_database_structure():
    """检查VeighNa数据库结构"""
    print("\n🔍 检查VeighNa数据库结构")
    print("=" * 50)
    
    vnpy_db = "f:/vn/database.db"
    
    if not os.path.exists(vnpy_db):
        print("❌ VeighNa数据库文件不存在")
        print("💡 解决方案: 需要先启动VeighNa创建数据库")
        return False
    
    try:
        conn = sqlite3.connect(vnpy_db)
        
        # 检查是否有dbbardata表
        tables = pd.read_sql("SELECT name FROM sqlite_master WHERE type='table'", conn)
        
        if 'dbbardata' in tables['name'].values:
            print("✅ 找到VeighNa标准数据表 'dbbardata'")
            
            # 检查数据量
            count = pd.read_sql("SELECT COUNT(*) as count FROM dbbardata", conn)['count'].iloc[0]
            print(f"📊 数据量: {count:,} 条K线")
            
            if count > 0:
                # 显示部分数据
                sample = pd.read_sql(
                    "SELECT symbol, exchange, datetime, open, high, low, close FROM dbbardata LIMIT 5", 
                    conn
                )
                print("\n📈 数据样本:")
                print(sample.to_string(index=False))
            else:
                print("⚠️  数据表为空")
                
        else:
            print("❌ 未找到VeighNa标准数据表 'dbbardata'")
            print("💡 这可能是数据未正确导入到VeighNa的原因")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查VeighNa数据库失败: {e}")
        return False

def import_data_to_vnpy():
    """将下载的数据导入到VeighNa数据库"""
    print("\n🔄 导入数据到VeighNa数据库")
    print("=" * 50)
    
    source_db = "f:/vn/optimized_stock_data.db"
    target_db = "f:/vn/database.db"
    
    if not os.path.exists(source_db):
        print("❌ 源数据库不存在，请先下载股票数据")
        return False
    
    try:
        # 连接源数据库
        source_conn = sqlite3.connect(source_db)
        
        # 获取股票数据
        stock_data = pd.read_sql(
            "SELECT * FROM stock_daily_data ORDER BY symbol, date", 
            source_conn
        )
        
        if stock_data.empty:
            print("❌ 源数据库中没有数据")
            source_conn.close()
            return False
        
        print(f"📊 找到 {len(stock_data)} 条数据记录")
        
        # 连接目标数据库
        target_conn = sqlite3.connect(target_db)
        
        # 创建VeighNa标准数据表
        target_conn.execute('''
            CREATE TABLE IF NOT EXISTS dbbardata (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                exchange TEXT NOT NULL,
                datetime TEXT NOT NULL,
                interval TEXT NOT NULL,
                volume REAL,
                turnover REAL,
                open_interest REAL,
                open REAL,
                high REAL,
                low REAL,
                close REAL,
                UNIQUE(symbol, exchange, datetime, interval)
            )
        ''')
        
        # 转换数据格式
        vnpy_data = []
        for _, row in stock_data.iterrows():
            vnpy_row = {
                'symbol': row['symbol'],
                'exchange': row['exchange'],
                'datetime': row['date'] + ' 15:00:00',  # 添加时间
                'interval': 'DAILY',
                'volume': row['volume'] if pd.notna(row['volume']) else 0,
                'turnover': row['amount'] if pd.notna(row['amount']) else 0,
                'open_interest': 0,
                'open': row['open_price'] if pd.notna(row['open_price']) else 0,
                'high': row['high_price'] if pd.notna(row['high_price']) else 0,
                'low': row['low_price'] if pd.notna(row['low_price']) else 0,
                'close': row['close_price'] if pd.notna(row['close_price']) else 0
            }
            vnpy_data.append(vnpy_row)
        
        # 批量插入数据
        vnpy_df = pd.DataFrame(vnpy_data)
        
        # 删除重复数据
        target_conn.execute("DELETE FROM dbbardata")
        
        # 插入新数据
        vnpy_df.to_sql('dbbardata', target_conn, if_exists='append', index=False)
        
        # 提交更改
        target_conn.commit()
        
        # 验证导入结果
        count = pd.read_sql("SELECT COUNT(*) as count FROM dbbardata", target_conn)['count'].iloc[0]
        print(f"✅ 成功导入 {count:,} 条数据到VeighNa数据库")
        
        # 显示导入的股票列表
        stocks = pd.read_sql(
            "SELECT DISTINCT symbol, exchange FROM dbbardata ORDER BY symbol LIMIT 10", 
            target_conn
        )
        print("\n📋 已导入的股票（前10只）:")
        for _, stock in stocks.iterrows():
            print(f"   {stock['symbol']}.{stock['exchange']}")
        
        source_conn.close()
        target_conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 导入数据失败: {e}")
        return False

def check_vt_setting():
    """检查VeighNa配置文件"""
    print("\n🔍 检查VeighNa配置文件")
    print("=" * 50)
    
    config_file = "f:/vn/vt_setting.json"
    
    if os.path.exists(config_file):
        try:
            import json
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print("✅ 配置文件存在")
            print(f"📊 数据源: {config.get('datafeed.name', '未配置')}")
            print(f"💾 数据库: {config.get('database.driver', '未配置')}")
            print(f"📁 数据库文件: {config.get('database.database', '未配置')}")
            
            return True
            
        except Exception as e:
            print(f"❌ 读取配置文件失败: {e}")
            return False
    else:
        print("❌ 配置文件不存在")
        return False

def provide_solutions():
    """提供解决方案"""
    print("\n💡 解决方案")
    print("=" * 50)
    
    print("\n🎯 如果在VeighNa中看不到数据，请按以下步骤操作:")
    
    print("\n1️⃣ 重启VeighNa")
    print("   - 完全关闭VeighNa程序")
    print("   - 重新运行: python start_vnpy_a_stock.py")
    
    print("\n2️⃣ 检查数据管理器")
    print("   - 在VeighNa中点击 '系统' -> '数据管理'")
    print("   - 输入股票代码: 000001")
    print("   - 选择交易所: SZSE")
    print("   - 点击 '查询' 按钮")
    
    print("\n3️⃣ 使用正确的股票代码格式")
    print("   - 深交所股票: 000001.SZSE")
    print("   - 上交所股票: 600000.SSE")
    print("   - 注意大小写和格式")
    
    print("\n4️⃣ 检查K线图表")
    print("   - 点击 '应用' -> 'K线图表'")
    print("   - 输入: 000001.SZSE")
    print("   - 选择周期: 日线")
    print("   - 点击 '查询'")
    
    print("\n5️⃣ 如果仍然看不到数据")
    print("   - 运行本脚本的导入功能")
    print("   - 检查数据库文件权限")
    print("   - 重新下载数据")

def main():
    """主函数"""
    print("🔧 VeighNa数据显示问题诊断工具")
    print("=" * 60)
    
    # 1. 检查数据库文件
    check_database_files()
    
    # 2. 检查VeighNa数据库结构
    vnpy_ok = check_vnpy_database_structure()
    
    # 3. 检查配置文件
    check_vt_setting()
    
    # 4. 如果VeighNa数据库为空，尝试导入数据
    if not vnpy_ok or True:  # 总是尝试导入以确保数据最新
        print("\n🔄 尝试导入数据到VeighNa...")
        import_success = import_data_to_vnpy()
        
        if import_success:
            print("\n✅ 数据导入完成！")
            print("💡 请重启VeighNa查看数据")
        else:
            print("\n❌ 数据导入失败")
    
    # 5. 提供解决方案
    provide_solutions()
    
    print("\n" + "=" * 60)
    print("🎯 建议: 运行完本脚本后，重启VeighNa并尝试查看数据")

if __name__ == "__main__":
    main()