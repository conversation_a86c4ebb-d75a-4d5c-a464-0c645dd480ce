#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
诊断VeighNa数据下载问题

本脚本用于诊断和解决数据下载失败的问题
"""

import akshare as ak
import pandas as pd
from datetime import datetime, <PERSON><PERSON><PERSON>

def check_stock_code_format(stock_code):
    """
    检查股票代码格式
    """
    print(f"=== 检查股票代码格式: {stock_code} ===")
    
    # 检查代码长度
    if len(stock_code) != 6:
        print(f"✗ 股票代码长度错误: {len(stock_code)}位，应为6位")
        return False
    
    # 检查是否为数字
    if not stock_code.isdigit():
        print(f"✗ 股票代码包含非数字字符: {stock_code}")
        return False
    
    # 判断交易所
    if stock_code.startswith(('600', '601', '603', '605', '688')):
        exchange = "上海证券交易所(SSE)"
        exchange_code = "SSE"
    elif stock_code.startswith(('000', '001', '002', '003', '300')):
        exchange = "深圳证券交易所(SZSE)"
        exchange_code = "SZSE"
    else:
        print(f"✗ 无法识别的股票代码前缀: {stock_code[:3]}")
        return False
    
    print(f"✓ 股票代码格式正确")
    print(f"  代码: {stock_code}")
    print(f"  交易所: {exchange}")
    print(f"  VeighNa交易所代码: {exchange_code}")
    
    return True, exchange_code

def test_akshare_data_availability(stock_code):
    """
    测试AkShare数据可用性
    """
    print(f"\n=== 测试AkShare数据可用性: {stock_code} ===")
    
    try:
        # 1. 检查股票是否存在
        print("1. 检查股票是否存在...")
        stock_info = ak.stock_info_a_code_name()
        stock_exists = stock_code in stock_info['code'].values
        
        if stock_exists:
            stock_name = stock_info[stock_info['code'] == stock_code]['name'].iloc[0]
            print(f"✓ 股票存在: {stock_code} - {stock_name}")
        else:
            print(f"✗ 股票不存在: {stock_code}")
            print("  可能原因:")
            print("  - 股票代码错误")
            print("  - 股票已退市")
            print("  - 新股尚未纳入数据源")
            return False
        
        # 2. 测试实时数据
        print("\n2. 测试实时数据...")
        try:
            realtime_data = ak.stock_zh_a_spot_em()
            stock_realtime = realtime_data[realtime_data['代码'] == stock_code]
            
            if not stock_realtime.empty:
                data = stock_realtime.iloc[0]
                print(f"✓ 实时数据可用")
                print(f"  最新价: {data['最新价']} 元")
                print(f"  成交量: {data['成交量']} 手")
            else:
                print(f"⚠ 实时数据不可用")
        except Exception as e:
            print(f"⚠ 实时数据获取失败: {e}")
        
        # 3. 测试历史数据
        print("\n3. 测试历史数据...")
        try:
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=7)).strftime('%Y%m%d')
            
            hist_data = ak.stock_zh_a_hist(
                symbol=stock_code, 
                period="daily", 
                start_date=start_date, 
                end_date=end_date, 
                adjust=""
            )
            
            if not hist_data.empty:
                print(f"✓ 历史数据可用")
                print(f"  数据条数: {len(hist_data)}")
                print(f"  日期范围: {hist_data['日期'].min()} 到 {hist_data['日期'].max()}")
                return True
            else:
                print(f"✗ 历史数据为空")
                return False
                
        except Exception as e:
            print(f"✗ 历史数据获取失败: {e}")
            return False
            
    except Exception as e:
        print(f"✗ 数据可用性测试失败: {e}")
        return False

def check_date_range(start_date, end_date):
    """
    检查日期范围
    """
    print(f"\n=== 检查日期范围 ===")
    
    try:
        # 解析日期
        if isinstance(start_date, str):
            start_dt = datetime.strptime(start_date, '%Y/%m/%d')
        else:
            start_dt = start_date
            
        if isinstance(end_date, str):
            end_dt = datetime.strptime(end_date, '%Y/%m/%d')
        else:
            end_dt = end_date
        
        now = datetime.now()
        
        print(f"开始日期: {start_dt.strftime('%Y-%m-%d')}")
        print(f"结束日期: {end_dt.strftime('%Y-%m-%d')}")
        print(f"当前日期: {now.strftime('%Y-%m-%d')}")
        
        # 检查日期逻辑
        if start_dt > end_dt:
            print("✗ 开始日期晚于结束日期")
            return False
        
        if end_dt > now:
            print("⚠ 结束日期是未来日期")
        
        if start_dt > now:
            print("✗ 开始日期是未来日期")
            return False
        
        # 检查是否包含交易日
        days_diff = (end_dt - start_dt).days
        print(f"日期跨度: {days_diff} 天")
        
        if days_diff > 365 * 5:
            print("⚠ 日期跨度过大，可能影响下载速度")
        
        # 检查是否为周末
        if start_dt.weekday() >= 5 and end_dt.weekday() >= 5:
            print("⚠ 日期范围可能只包含周末，无交易数据")
        
        print("✓ 日期范围检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 日期范围检查失败: {e}")
        return False

def check_vnpy_datafeed():
    """
    检查VeighNa数据源配置
    """
    print(f"\n=== 检查VeighNa数据源配置 ===")
    
    try:
        from vnpy.trader.datafeed import get_datafeed
        from vnpy.trader.setting import SETTINGS
        
        # 检查配置
        datafeed_name = SETTINGS.get("datafeed.name")
        print(f"配置的数据源: {datafeed_name}")
        
        if datafeed_name != "akshare":
            print(f"✗ 数据源配置错误，应为'akshare'，当前为'{datafeed_name}'")
            return False
        
        # 获取数据源实例
        datafeed = get_datafeed()
        print(f"数据源类型: {datafeed.__class__.__name__}")
        print(f"数据源模块: {datafeed.__class__.__module__}")
        
        if 'akshare' not in datafeed.__class__.__module__.lower():
            print(f"✗ 数据源未正确加载AkShare")
            return False
        
        print("✓ VeighNa数据源配置正确")
        return True
        
    except Exception as e:
        print(f"✗ VeighNa数据源检查失败: {e}")
        return False

def provide_solutions():
    """
    提供解决方案
    """
    print(f"\n=== 常见问题解决方案 ===")
    
    print("\n1. 股票代码问题:")
    print("   - 确保代码为6位数字")
    print("   - 上证股票: 600xxx, 601xxx, 603xxx, 605xxx, 688xxx")
    print("   - 深证股票: 000xxx, 001xxx, 002xxx, 003xxx, 300xxx")
    print("   - 不要包含交易所后缀(.SSE, .SZSE)")
    
    print("\n2. 交易所选择:")
    print("   - 上证股票选择: SSE")
    print("   - 深证股票选择: SZSE")
    print("   - 必须与股票代码匹配")
    
    print("\n3. 日期设置:")
    print("   - 避免选择周末和节假日")
    print("   - 不要选择未来日期")
    print("   - 建议选择最近的交易日")
    
    print("\n4. 数据周期:")
    print("   - 日线数据: DAILY")
    print("   - 周线数据: WEEKLY")
    print("   - 月线数据: MONTHLY")
    print("   - 分钟数据可能需要特殊权限")
    
    print("\n5. 网络问题:")
    print("   - 确保网络连接正常")
    print("   - 某些时段数据源可能维护")
    print("   - 尝试稍后重试")

def diagnose_common_cases():
    """
    诊断常见案例
    """
    print(f"\n=== 诊断常见案例 ===")
    
    # 测试用户输入的代码
    test_code = "000004"  # 从截图看到的代码
    
    print(f"\n诊断用户输入: {test_code}")
    print("-" * 30)
    
    # 检查代码格式
    format_result = check_stock_code_format(test_code)
    if not format_result:
        return
    
    # 检查数据可用性
    data_result = test_akshare_data_availability(test_code)
    
    # 检查VeighNa配置
    config_result = check_vnpy_datafeed()
    
    # 总结
    print(f"\n=== 诊断结果 ===")
    print(f"代码格式: {'✓' if format_result else '✗'}")
    print(f"数据可用: {'✓' if data_result else '✗'}")
    print(f"配置正确: {'✓' if config_result else '✗'}")
    
    if format_result and data_result and config_result:
        print("\n🎉 所有检查通过！数据应该可以正常下载。")
        print("\n如果仍然无法下载，请尝试:")
        print("1. 重启VeighNa")
        print("2. 检查网络连接")
        print("3. 稍后重试")
    else:
        print("\n⚠ 发现问题，请参考解决方案")

def main():
    """
    主函数
    """
    print("VeighNa数据下载问题诊断工具")
    print("=" * 50)
    
    # 运行诊断
    diagnose_common_cases()
    
    # 提供解决方案
    provide_solutions()
    
    print("\n=== 推荐操作步骤 ===")
    print("\n1. 验证股票代码:")
    print("   - 使用6位数字代码")
    print("   - 确认股票存在且正常交易")
    
    print("\n2. 正确选择交易所:")
    print("   - 000004 应选择 SZSE (深圳证券交易所)")
    
    print("\n3. 设置合理日期:")
    print("   - 开始日期: 最近1个月内的交易日")
    print("   - 结束日期: 昨天或更早")
    
    print("\n4. 选择数据周期:")
    print("   - 建议先尝试 DAILY (日线)")
    
    print("\n5. 如果仍有问题:")
    print("   - 重启VeighNa应用")
    print("   - 检查网络连接")
    print("   - 尝试其他股票代码")

if __name__ == "__main__":
    main()