#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VeighNa配置AkShare数据源获取上证深证数据示例

本脚本演示如何：
1. 配置AkShare数据源
2. 下载上证和深证股票历史数据
3. 订阅实时行情数据
"""

import json
import os
from datetime import datetime, timedelta
from vnpy.event import EventEngine
from vnpy.trader.engine import MainEngine
from vnpy.trader.ui import MainWindow, create_qapp
from vnpy.trader.constant import Exchange, Interval
from vnpy.trader.object import HistoryRequest

# 导入AkShare数据源
from vnpy_akshare import Datafeed as AkshareDatafeed

# 导入应用模块
from vnpy_datamanager import DataManagerApp
from vnpy_ctastrategy import CtaStrategyApp

def configure_akshare_datafeed():
    """
    配置AkShare数据源
    
    AkShare是免费的金融数据接口，支持上证和深证所有股票数据
    """
    print("=== 配置AkShare数据源 ===")
    
    # 配置文件路径
    config_file = "vt_setting.json"
    
    # 默认配置
    config = {
        "datafeed.name": "akshare",
        "datafeed.username": "token",  # AkShare不需要真实用户名
        "datafeed.password": "token",  # AkShare不需要真实密码
        "database.driver": "sqlite",
        "database.database": "database.db"
    }
    
    # 如果配置文件存在，读取现有配置
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                existing_config = json.load(f)
                config.update(existing_config)
        except Exception as e:
            print(f"读取配置文件失败: {e}")
    
    # 写入配置文件
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4, ensure_ascii=False)
        print("✓ AkShare数据源配置完成")
        print(f"  配置文件: {config_file}")
        print(f"  数据源: {config['datafeed.name']}")
        return True
    except Exception as e:
        print(f"✗ 配置失败: {e}")
        return False

def get_sample_stocks():
    """
    获取示例股票列表
    
    返回上证和深证的代表性股票
    """
    stocks = {
        "上证股票": [
            {"symbol": "600000.SSE", "name": "浦发银行"},
            {"symbol": "600036.SSE", "name": "招商银行"},
            {"symbol": "600519.SSE", "name": "贵州茅台"},
            {"symbol": "601318.SSE", "name": "中国平安"},
            {"symbol": "601398.SSE", "name": "工商银行"},
        ],
        "深证股票": [
            {"symbol": "000001.SZSE", "name": "平安银行"},
            {"symbol": "000002.SZSE", "name": "万科A"},
            {"symbol": "000858.SZSE", "name": "五粮液"},
            {"symbol": "000333.SZSE", "name": "美的集团"},
            {"symbol": "000651.SZSE", "name": "格力电器"},
        ]
    }
    return stocks

def download_history_data(main_engine):
    """
    下载历史数据示例
    
    使用数据管理器下载上证和深证股票的历史数据
    """
    print("\n=== 下载历史数据 ===")
    
    # 获取数据管理器
    data_manager = main_engine.get_app("DataManager")
    if not data_manager:
        print("✗ 数据管理器未启动")
        return
    
    # 获取示例股票
    stocks = get_sample_stocks()
    
    # 设置下载参数
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)  # 下载最近30天数据
    
    print(f"下载时间范围: {start_date.date()} 到 {end_date.date()}")
    
    # 下载数据
    for exchange_name, stock_list in stocks.items():
        print(f"\n--- {exchange_name} ---")
        
        for stock in stock_list[:2]:  # 只下载前2只股票作为示例
            symbol = stock["symbol"]
            name = stock["name"]
            
            print(f"下载 {symbol} ({name}) 的历史数据...")
            
            try:
                # 创建历史数据请求
                req = HistoryRequest(
                    symbol=symbol.split('.')[0],  # 股票代码
                    exchange=Exchange.SSE if 'SSE' in symbol else Exchange.SZSE,
                    start=start_date,
                    end=end_date,
                    interval=Interval.DAILY
                )
                
                # 这里只是示例，实际下载需要通过数据管理器界面操作
                print(f"  ✓ 请求创建成功: {req.symbol}")
                
            except Exception as e:
                print(f"  ✗ 下载失败: {e}")

def subscribe_realtime_data(main_engine):
    """
    订阅实时行情数据示例
    """
    print("\n=== 订阅实时行情 ===")
    
    # 获取示例股票
    stocks = get_sample_stocks()
    
    print("可订阅的股票代码:")
    for exchange_name, stock_list in stocks.items():
        print(f"\n{exchange_name}:")
        for stock in stock_list:
            print(f"  {stock['symbol']} - {stock['name']}")
    
    print("\n在VeighNa主界面的交易组件中:")
    print("1. 选择交易所: SSE 或 SZSE")
    print("2. 输入股票代码: 如 600000 或 000001")
    print("3. 按回车键订阅行情")

def create_sample_strategy():
    """
    创建示例策略代码
    """
    strategy_code = '''
from vnpy_ctastrategy import CtaTemplate
from vnpy.trader.object import TickData, BarData
from vnpy.trader.constant import Direction, Offset

class SSESZSEStrategy(CtaTemplate):
    """上证深证股票策略示例"""
    
    author = "VeighNa User"
    
    # 策略参数
    fast_window = 10
    slow_window = 20
    fixed_size = 100
    
    # 策略变量
    fast_ma = 0.0
    slow_ma = 0.0
    
    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)
        
        # 订阅多个股票
        self.symbols = [
            "600000.SSE",  # 浦发银行
            "000001.SZSE", # 平安银行
        ]
        
        for symbol in self.symbols:
            self.subscribe(symbol)
    
    def on_tick(self, tick: TickData):
        """处理实时行情数据"""
        if tick.vt_symbol.endswith(".SSE"):
            self.write_log(f"上证股票 {tick.vt_symbol} 最新价: {tick.last_price}")
        elif tick.vt_symbol.endswith(".SZSE"):
            self.write_log(f"深证股票 {tick.vt_symbol} 最新价: {tick.last_price}")
    
    def on_bar(self, bar: BarData):
        """处理K线数据"""
        # 计算移动平均线
        self.fast_ma = self.am.sma(self.fast_window)
        self.slow_ma = self.am.sma(self.slow_window)
        
        # 交易逻辑
        if self.fast_ma > self.slow_ma and self.pos == 0:
            self.buy(bar.close_price, self.fixed_size)
        elif self.fast_ma < self.slow_ma and self.pos > 0:
            self.sell(bar.close_price, self.pos)
'''
    
    # 保存策略文件
    strategy_file = "sse_szse_strategy.py"
    try:
        with open(strategy_file, 'w', encoding='utf-8') as f:
            f.write(strategy_code)
        print(f"\n=== 示例策略已创建 ===")
        print(f"策略文件: {strategy_file}")
        print("您可以在CTA策略模块中加载此策略")
    except Exception as e:
        print(f"创建策略文件失败: {e}")

def main():
    """
    主函数
    """
    print("VeighNa AkShare数据源配置工具")
    print("=" * 50)
    
    # 1. 配置AkShare数据源
    if not configure_akshare_datafeed():
        return
    
    # 2. 创建VeighNa应用
    print("\n=== 启动VeighNa平台 ===")
    
    try:
        # 创建Qt应用
        qapp = create_qapp()
        
        # 创建事件引擎
        event_engine = EventEngine()
        
        # 创建主引擎
        main_engine = MainEngine(event_engine)
        
        # 添加应用模块
        main_engine.add_app(DataManagerApp)
        main_engine.add_app(CtaStrategyApp)
        print("✓ 应用模块已加载")
        
        # 验证数据源配置
        from vnpy.trader.datafeed import get_datafeed
        datafeed = get_datafeed()
        print(f"✓ 数据源已配置: {datafeed.__class__.__name__}")
        
        # 创建主窗口
        main_window = MainWindow(main_engine, event_engine)
        main_window.showMaximized()
        
        print("✓ VeighNa平台启动成功")
        
        # 3. 显示使用指南
        download_history_data(main_engine)
        subscribe_realtime_data(main_engine)
        create_sample_strategy()
        
        print("\n=== 使用说明 ===")
        print("1. 在'功能'菜单中打开'数据管理'模块下载历史数据")
        print("2. 在交易界面订阅实时行情")
        print("3. 在'功能'菜单中打开'CTA策略'模块运行策略")
        print("4. 查看生成的示例策略文件进行学习")
        
        # 运行应用
        qapp.exec()
        
    except Exception as e:
        print(f"✗ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()