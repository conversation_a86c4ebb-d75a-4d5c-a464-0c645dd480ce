#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VeighNa 基础功能测试脚本
"""

from vnpy.event import EventEngine
from vnpy.trader.engine import MainEngine
from vnpy.trader.object import TickData, BarData
from vnpy.trader.constant import Exchange, Interval
from datetime import datetime
import sys

def test_basic_functionality():
    """测试VeighNa基础功能"""
    print("=" * 50)
    print("VeighNa 基础功能测试")
    print("=" * 50)
    
    try:
        # 测试事件引擎
        print("1. 测试事件引擎...")
        event_engine = EventEngine()
        print("   ✓ 事件引擎创建成功")
        
        # 测试主引擎
        print("2. 测试主引擎...")
        main_engine = MainEngine(event_engine)
        print("   ✓ 主引擎创建成功")
        
        # 测试数据对象创建
        print("3. 测试数据对象...")
        
        # 创建Tick数据
        tick = TickData(
            symbol="rb2501",
            exchange=Exchange.SHFE,
            datetime=datetime.now(),
            name="螺纹钢2501",
            last_price=3500.0,
            volume=1000,
            gateway_name="test"
        )
        print(f"   ✓ Tick数据创建成功: {tick.symbol}@{tick.exchange.value}")
        
        # 创建Bar数据
        bar = BarData(
            symbol="rb2501",
            exchange=Exchange.SHFE,
            datetime=datetime.now(),
            interval=Interval.MINUTE,
            volume=1000,
            open_price=3500.0,
            high_price=3510.0,
            low_price=3490.0,
            close_price=3505.0,
            gateway_name="test"
        )
        print(f"   ✓ Bar数据创建成功: {bar.symbol}@{bar.exchange.value}")
        
        # 测试技术指标库
        print("4. 测试技术指标库...")
        import talib
        import numpy as np
        
        # 创建测试数据
        close_prices = np.array([3500, 3510, 3505, 3520, 3515, 3530, 3525, 3540, 3535, 3550], dtype=float)
        
        # 计算简单移动平均线
        sma = talib.SMA(close_prices, timeperiod=5)
        print(f"   ✓ SMA计算成功: 最新值 {sma[-1]:.2f}")
        
        # 计算RSI
        rsi = talib.RSI(close_prices, timeperiod=5)
        print(f"   ✓ RSI计算成功: 最新值 {rsi[-1]:.2f}")
        
        # 清理资源
        print("   ✓ 资源清理完成")
        
        print("\n" + "=" * 50)
        print("🎉 所有测试通过！VeighNa安装成功！")
        print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def show_vnpy_info():
    """显示VeighNa版本和模块信息"""
    import vnpy
    print(f"\nVeighNa版本: {vnpy.__version__}")
    print(f"Python版本: {sys.version}")
    
    # 检查可用模块
    print("\n可用的核心模块:")
    modules = [
        ('vnpy.event', '事件引擎'),
        ('vnpy.trader', '交易引擎'),
        ('vnpy.chart', '图表模块'),
        ('vnpy.rpc', 'RPC通讯'),
        ('vnpy.alpha', 'AI量化模块'),
        ('talib', '技术分析库')
    ]
    
    for module_name, description in modules:
        try:
            __import__(module_name)
            print(f"  ✓ {module_name} - {description}")
        except ImportError:
            print(f"  ✗ {module_name} - {description} (未安装)")

if __name__ == "__main__":
    show_vnpy_info()
    test_basic_functionality()