#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
优化版A股数据下载工具
解决下载失败率高的问题
"""

import akshare as ak
import pandas as pd
import sqlite3
import time
import random
from datetime import datetime, timedelta
from pathlib import Path
import logging

class OptimizedAStockDownloader:
    """优化版A股数据下载器"""
    
    def __init__(self, db_path="f:/vn/optimized_stock_data.db", start_date=None):
        self.db_path = db_path
        self.start_date = start_date or (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
        self.end_date = datetime.now().strftime('%Y%m%d')
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('download_log.txt', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # 统计信息
        self.stats = {
            'total': 0,
            'success': 0,
            'failed': 0,
            'skipped': 0,
            'total_bars': 0
        }
        
        # 失败重试配置
        self.max_retries = 3
        self.retry_delay = 2
        
    def create_database_table(self):
        """创建数据库表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS stock_daily_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                name TEXT,
                exchange TEXT,
                date TEXT NOT NULL,
                open_price REAL,
                high_price REAL,
                low_price REAL,
                close_price REAL,
                volume INTEGER,
                amount REAL,
                turnover_rate REAL,
                pe_ratio REAL,
                pb_ratio REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(symbol, date)
            )
        ''')
        
        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_symbol_date ON stock_daily_data(symbol, date)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_date ON stock_daily_data(date)')
        
        conn.commit()
        conn.close()
        
        self.logger.info("数据库表创建完成")
    
    def get_valid_stock_list(self):
        """获取有效的股票列表（过滤掉已退市和ST股票）"""
        try:
            self.logger.info("正在获取股票列表...")
            
            # 获取A股列表
            stock_list = ak.stock_info_a_code_name()
            
            # 过滤条件
            valid_stocks = []
            for _, row in stock_list.iterrows():
                code = row['code']
                name = row['name']
                
                # 跳过ST股票、退市股票等
                if any(keyword in name for keyword in ['ST', '*ST', 'PT', '退市']):
                    continue
                    
                # 跳过科创板和创业板的部分问题股票
                if code.startswith('688') or code.startswith('300'):
                    # 只保留部分主要的科创板和创业板股票
                    if len(valid_stocks) > 1000:  # 限制总数
                        continue
                
                valid_stocks.append({
                    'code': code,
                    'name': name,
                    'exchange': 'SSE' if code.startswith('6') else 'SZSE'
                })
            
            self.logger.info(f"获取到 {len(valid_stocks)} 只有效股票")
            return valid_stocks
            
        except Exception as e:
            self.logger.error(f"获取股票列表失败: {e}")
            return []
    
    def download_single_stock(self, stock_code, stock_name, exchange, retry_count=0):
        """下载单只股票数据（带重试机制）"""
        try:
            # 检查是否已存在数据
            if self.check_data_exists(stock_code):
                self.stats['skipped'] += 1
                return 0, "数据已存在，跳过"
            
            # 随机延迟，避免请求过于频繁
            time.sleep(random.uniform(0.1, 0.5))
            
            # 获取股票历史数据
            df = ak.stock_zh_a_hist(
                symbol=stock_code,
                period="daily",
                start_date=self.start_date,
                end_date=self.end_date,
                adjust="qfq"  # 前复权
            )
            
            if df.empty:
                self.stats['failed'] += 1
                return 0, "无数据"
            
            # 数据清洗和格式化
            df = df.rename(columns={
                '日期': 'date',
                '开盘': 'open_price',
                '最高': 'high_price',
                '最低': 'low_price',
                '收盘': 'close_price',
                '成交量': 'volume',
                '成交额': 'amount',
                '振幅': 'amplitude',
                '涨跌幅': 'pct_change',
                '涨跌额': 'change',
                '换手率': 'turnover_rate'
            })
            
            # 添加股票信息
            df['symbol'] = stock_code
            df['name'] = stock_name
            df['exchange'] = exchange
            
            # 保存到数据库
            conn = sqlite3.connect(self.db_path)
            
            # 只保存必要字段
            save_df = df[[
                'symbol', 'name', 'exchange', 'date',
                'open_price', 'high_price', 'low_price', 'close_price',
                'volume', 'amount', 'turnover_rate'
            ]].copy()
            
            save_df.to_sql('stock_daily_data', conn, if_exists='append', index=False)
            conn.close()
            
            self.stats['success'] += 1
            self.stats['total_bars'] += len(df)
            
            return len(df), "成功"
            
        except Exception as e:
            error_msg = str(e)
            
            # 如果是网络错误或API限制，进行重试
            if retry_count < self.max_retries and any(keyword in error_msg.lower() for keyword in 
                ['timeout', 'connection', 'network', 'rate limit', '请求过于频繁']):
                
                self.logger.warning(f"下载 {stock_code} 失败，{self.retry_delay}秒后重试 ({retry_count+1}/{self.max_retries}): {error_msg}")
                time.sleep(self.retry_delay)
                return self.download_single_stock(stock_code, stock_name, exchange, retry_count + 1)
            
            self.stats['failed'] += 1
            self.logger.error(f"下载 {stock_code} 最终失败: {error_msg}")
            return 0, f"失败: {error_msg}"
    
    def check_data_exists(self, stock_code):
        """检查股票数据是否已存在"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(
                "SELECT COUNT(*) FROM stock_daily_data WHERE symbol = ? AND date >= ?",
                (stock_code, self.start_date)
            )
            
            count = cursor.fetchone()[0]
            conn.close()
            
            return count > 0
            
        except:
            return False
    
    def download_batch_stocks(self, stock_list, batch_size=100):
        """批量下载股票数据"""
        total_stocks = len(stock_list)
        self.stats['total'] = total_stocks
        
        self.logger.info(f"开始下载 {total_stocks} 只股票的数据")
        
        for i, stock in enumerate(stock_list, 1):
            try:
                # 显示进度
                progress = (i / total_stocks) * 100
                print(f"\r进度: {progress:.1f}% [{i}/{total_stocks}] "
                      f"成功={self.stats['success']} 失败={self.stats['failed']} "
                      f"跳过={self.stats['skipped']} 当前={stock['code']}({stock['name'][:2]})", end="")
                
                # 下载数据
                bars_count, status = self.download_single_stock(
                    stock['code'], stock['name'], stock['exchange']
                )
                
                # 每100只股票输出一次详细统计
                if i % batch_size == 0:
                    self.print_progress_summary(i, total_stocks)
                
                # 每500只股票暂停一下，避免API限制
                if i % 500 == 0:
                    self.logger.info(f"\n已完成 {i} 只股票，暂停30秒...")
                    time.sleep(30)
                    
            except KeyboardInterrupt:
                self.logger.info("\n用户中断下载")
                break
            except Exception as e:
                self.logger.error(f"处理股票 {stock['code']} 时出错: {e}")
                self.stats['failed'] += 1
        
        print()  # 换行
        self.print_final_summary()
    
    def print_progress_summary(self, current, total):
        """打印进度摘要"""
        success_rate = (self.stats['success'] / current) * 100 if current > 0 else 0
        
        print(f"\n📊 进度摘要 [{current}/{total}]:")
        print(f"   ✅ 成功: {self.stats['success']} ({success_rate:.1f}%)")
        print(f"   ❌ 失败: {self.stats['failed']}")
        print(f"   ⏭️  跳过: {self.stats['skipped']}")
        print(f"   📈 数据量: {self.stats['total_bars']:,} 条")
    
    def print_final_summary(self):
        """打印最终摘要"""
        total_processed = self.stats['success'] + self.stats['failed'] + self.stats['skipped']
        success_rate = (self.stats['success'] / total_processed) * 100 if total_processed > 0 else 0
        
        print("\n🎉 下载完成！")
        print("=" * 50)
        print(f"📊 总体统计:")
        print(f"   📋 总股票数: {self.stats['total']}")
        print(f"   ✅ 成功下载: {self.stats['success']} ({success_rate:.1f}%)")
        print(f"   ❌ 下载失败: {self.stats['failed']}")
        print(f"   ⏭️  数据跳过: {self.stats['skipped']}")
        print(f"   📈 总数据量: {self.stats['total_bars']:,} 条K线")
        print(f"   💾 数据库文件: {self.db_path}")
        
        # 计算数据库大小
        try:
            db_size = Path(self.db_path).stat().st_size / (1024 * 1024)  # MB
            print(f"   📁 数据库大小: {db_size:.1f} MB")
        except:
            pass
    
    def download_popular_stocks(self):
        """下载热门股票（高成功率）"""
        popular_stocks = [
            {'code': '000001', 'name': '平安银行', 'exchange': 'SZSE'},
            {'code': '000002', 'name': '万科A', 'exchange': 'SZSE'},
            {'code': '000858', 'name': '五粮液', 'exchange': 'SZSE'},
            {'code': '000333', 'name': '美的集团', 'exchange': 'SZSE'},
            {'code': '600000', 'name': '浦发银行', 'exchange': 'SSE'},
            {'code': '600036', 'name': '招商银行', 'exchange': 'SSE'},
            {'code': '600519', 'name': '贵州茅台', 'exchange': 'SSE'},
            {'code': '600887', 'name': '伊利股份', 'exchange': 'SSE'},
            {'code': '002415', 'name': '海康威视', 'exchange': 'SZSE'},
            {'code': '002594', 'name': '比亚迪', 'exchange': 'SZSE'}
        ]
        
        self.logger.info("开始下载热门股票数据")
        self.download_batch_stocks(popular_stocks)

def main():
    """主函数"""
    print("🚀 优化版A股数据下载工具")
    print("=" * 50)
    print("")
    print("📋 下载选项:")
    print("1. 🔥 热门股票 (10只，高成功率)")
    print("2. 📊 精选股票 (100只主要股票)")
    print("3. 🎯 全量下载 (所有有效股票，过滤ST等)")
    print("4. 🔧 自定义下载")
    print("0. 退出")
    print("")
    
    choice = input("请选择 (0-4): ").strip()
    
    if choice == "0":
        print("再见！")
        return
    
    # 创建下载器
    downloader = OptimizedAStockDownloader()
    downloader.create_database_table()
    
    if choice == "1":
        downloader.download_popular_stocks()
    
    elif choice == "2":
        stock_list = downloader.get_valid_stock_list()[:100]  # 前100只
        downloader.download_batch_stocks(stock_list)
    
    elif choice == "3":
        stock_list = downloader.get_valid_stock_list()
        downloader.download_batch_stocks(stock_list)
    
    elif choice == "4":
        print("\n自定义下载配置:")
        max_stocks = int(input("最大股票数量 (默认1000): ") or "1000")
        days = int(input("历史天数 (默认365): ") or "365")
        
        start_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')
        downloader.start_date = start_date
        
        stock_list = downloader.get_valid_stock_list()[:max_stocks]
        downloader.download_batch_stocks(stock_list)
    
    else:
        print("无效选择")

if __name__ == "__main__":
    main()