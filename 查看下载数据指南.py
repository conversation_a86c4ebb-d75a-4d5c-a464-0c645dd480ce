#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
查看下载的A股数据指南
帮助用户在VeighNa中查看已下载的股票数据
"""

import sqlite3
import pandas as pd
from pathlib import Path
import os

def check_downloaded_data():
    """检查已下载的数据概况"""
    print("📊 检查已下载的股票数据")
    print("=" * 50)
    
    # 检查数据库文件
    db_files = [
        "f:/vn/optimized_stock_data.db",
        "f:/vn/database.db",
        "f:/vn/stock_data.db"
    ]
    
    found_data = False
    
    for db_path in db_files:
        if os.path.exists(db_path):
            print(f"\n✅ 找到数据库文件: {db_path}")
            
            try:
                conn = sqlite3.connect(db_path)
                
                # 检查表结构
                tables = pd.read_sql("SELECT name FROM sqlite_master WHERE type='table'", conn)
                print(f"📋 数据表: {', '.join(tables['name'].tolist())}")
                
                # 检查股票数据
                if 'stock_daily_data' in tables['name'].values:
                    # 统计股票数量
                    stock_count = pd.read_sql(
                        "SELECT COUNT(DISTINCT symbol) as count FROM stock_daily_data", 
                        conn
                    )['count'].iloc[0]
                    
                    # 统计数据量
                    total_records = pd.read_sql(
                        "SELECT COUNT(*) as count FROM stock_daily_data", 
                        conn
                    )['count'].iloc[0]
                    
                    # 获取日期范围
                    date_range = pd.read_sql(
                        "SELECT MIN(date) as start_date, MAX(date) as end_date FROM stock_daily_data", 
                        conn
                    )
                    
                    print(f"📈 股票数量: {stock_count} 只")
                    print(f"📊 总数据量: {total_records:,} 条K线")
                    print(f"📅 日期范围: {date_range['start_date'].iloc[0]} 至 {date_range['end_date'].iloc[0]}")
                    
                    # 显示部分股票列表
                    stocks = pd.read_sql(
                        "SELECT DISTINCT symbol, name FROM stock_daily_data ORDER BY symbol LIMIT 10", 
                        conn
                    )
                    print(f"\n📋 部分股票列表:")
                    for _, row in stocks.iterrows():
                        print(f"   {row['symbol']} - {row['name']}")
                    
                    if stock_count > 10:
                        print(f"   ... 还有 {stock_count - 10} 只股票")
                    
                    found_data = True
                
                elif 'dbbardata' in tables['name'].values:
                    # VeighNa标准数据表
                    stock_count = pd.read_sql(
                        "SELECT COUNT(DISTINCT symbol) as count FROM dbbardata", 
                        conn
                    )['count'].iloc[0]
                    
                    total_records = pd.read_sql(
                        "SELECT COUNT(*) as count FROM dbbardata", 
                        conn
                    )['count'].iloc[0]
                    
                    print(f"📈 股票数量: {stock_count} 只")
                    print(f"📊 总数据量: {total_records:,} 条K线")
                    
                    found_data = True
                
                conn.close()
                
            except Exception as e:
                print(f"❌ 读取数据库失败: {e}")
    
    if not found_data:
        print("\n❌ 未找到已下载的股票数据")
        print("请先运行数据下载工具")
    
    return found_data

def show_stock_data_sample(symbol="000001"):
    """显示指定股票的数据样本"""
    print(f"\n📈 查看股票 {symbol} 的数据样本")
    print("-" * 40)
    
    db_files = [
        "f:/vn/optimized_stock_data.db",
        "f:/vn/database.db"
    ]
    
    for db_path in db_files:
        if os.path.exists(db_path):
            try:
                conn = sqlite3.connect(db_path)
                
                # 查询股票数据
                query = f"""
                SELECT date, open_price, high_price, low_price, close_price, volume
                FROM stock_daily_data 
                WHERE symbol = '{symbol}' 
                ORDER BY date DESC 
                LIMIT 10
                """
                
                df = pd.read_sql(query, conn)
                
                if not df.empty:
                    print(f"\n最近10个交易日数据:")
                    print(df.to_string(index=False))
                    break
                else:
                    print(f"未找到股票 {symbol} 的数据")
                
                conn.close()
                
            except Exception as e:
                print(f"查询失败: {e}")

def show_vnpy_integration_guide():
    """显示VeighNa集成指南"""
    print("\n🛠️  在VeighNa中查看数据的方法")
    print("=" * 50)
    
    print("\n方法1: 使用VeighNa数据管理器")
    print("-" * 30)
    print("1. 启动VeighNa: python start_vnpy_a_stock.py")
    print("2. 在主界面点击 '系统' -> '数据管理'")
    print("3. 选择股票代码和日期范围")
    print("4. 点击 '查询' 查看历史数据")
    
    print("\n方法2: 在K线图中查看")
    print("-" * 30)
    print("1. 启动VeighNa后，点击 '应用' -> 'K线图表'")
    print("2. 输入股票代码（如：000001.SZSE）")
    print("3. 选择时间周期（日线、周线等）")
    print("4. 点击 '查询' 显示K线图")
    
    print("\n方法3: 在策略回测中使用")
    print("-" * 30)
    print("1. 点击 '应用' -> 'CTA策略'")
    print("2. 在回测界面选择股票和时间范围")
    print("3. 加载策略进行历史回测")
    
    print("\n📋 常用股票代码格式:")
    print("   深交所: 000001.SZSE (平安银行)")
    print("   上交所: 600000.SSE (浦发银行)")
    print("   创业板: 300001.SZSE")
    print("   科创板: 688001.SSE")

def export_stock_list():
    """导出股票列表到文件"""
    print("\n📄 导出股票列表")
    print("-" * 30)
    
    db_files = [
        "f:/vn/optimized_stock_data.db",
        "f:/vn/database.db"
    ]
    
    for db_path in db_files:
        if os.path.exists(db_path):
            try:
                conn = sqlite3.connect(db_path)
                
                # 获取所有股票列表
                stocks = pd.read_sql(
                    "SELECT DISTINCT symbol, name, exchange FROM stock_daily_data ORDER BY symbol", 
                    conn
                )
                
                if not stocks.empty:
                    # 保存到CSV文件
                    output_file = "f:/vn/已下载股票列表.csv"
                    stocks.to_csv(output_file, index=False, encoding='utf-8-sig')
                    print(f"✅ 股票列表已导出到: {output_file}")
                    print(f"📊 共 {len(stocks)} 只股票")
                    
                    # 显示前10只股票
                    print("\n前10只股票:")
                    for _, row in stocks.head(10).iterrows():
                        print(f"   {row['symbol']} - {row['name']} ({row['exchange']})")
                    
                    break
                
                conn.close()
                
            except Exception as e:
                print(f"导出失败: {e}")

def main():
    """主函数"""
    print("🎉 恭喜！您已成功下载了96只股票的数据")
    print("现在让我们来查看这些数据")
    print("\n" + "=" * 60)
    
    # 检查数据概况
    has_data = check_downloaded_data()
    
    if has_data:
        # 显示数据样本
        show_stock_data_sample("000001")
        
        # 显示VeighNa集成指南
        show_vnpy_integration_guide()
        
        # 导出股票列表
        export_stock_list()
        
        print("\n🎯 下一步建议:")
        print("1. 启动VeighNa: python start_vnpy_a_stock.py")
        print("2. 在VeighNa中查看和使用这些数据")
        print("3. 尝试策略回测或实时分析")
        
    print("\n" + "=" * 60)
    print("📚 更多帮助请查看: A股数据下载问题解决方案.md")

if __name__ == "__main__":
    main()